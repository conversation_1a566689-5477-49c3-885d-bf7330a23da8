package com.example.castapp.ui.dialog

import android.app.Dialog
import android.content.Context
import android.graphics.RectF
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageButton
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.example.castapp.R
import com.example.castapp.model.RemoteReceiverConnection
import com.example.castapp.ui.view.WindowContainerVisualizationView
import com.example.castapp.utils.AppLog
import com.example.castapp.utils.WindowScaleCalculator
import com.example.castapp.model.WindowVisualizationData
import com.example.castapp.websocket.ControlMessage
import com.example.castapp.model.WindowUpdateMode

/**
 * 远程接收端控制对话框
 * 用于控制远程接收端设备
 */
class RemoteReceiverControlDialog(
    private var remoteReceiverConnection: RemoteReceiverConnection
) : DialogFragment() {

    // UI组件
    private lateinit var dialogTitle: TextView
    private lateinit var closeButton: ImageButton
    private lateinit var clearScreenButton: Button
    private lateinit var saveButton: Button
    private lateinit var broadcastButton: Button
    private lateinit var windowButton: Button
    private lateinit var receiveButton: Button
    private lateinit var updateButton: Button

    // 🪟 窗口容器可视化组件
    private lateinit var windowVisualizationView: WindowContainerVisualizationView

    // 🔄 缓存的窗口信息列表，供窗口设置窗口使用
    private var cachedWindowInfoList: List<com.example.castapp.model.CastWindowInfo> = emptyList()

    // 缩放计算相关
    private var remoteControlScale: Double = 1.0

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        
        // 设置Dialog样式
        dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)
        
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_remote_receiver_control, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews(view)
        setupClickListeners()
        setupUI()
        registerForConnectionStateUpdates()

        // 🪟 自动获取初始窗口信息并启用可视化
        if (remoteReceiverConnection.isConnected) {
            requestInitialWindowInfoForVisualization()
        }

        AppLog.d("远程接收端控制对话框已创建: ${remoteReceiverConnection.deviceName}")
    }

    override fun onStart() {
        super.onStart()

        // 🚀 新增：基于接收端屏幕分辨率的自适应窗口尺寸
        val (dialogWidth, dialogHeight) = calculateAdaptiveDialogSize()

        // 设置Dialog大小
        dialog?.window?.setLayout(dialogWidth, dialogHeight)

        AppLog.d("远程接收端控制窗口尺寸: ${dialogWidth}×${dialogHeight} (基于接收端分辨率: ${remoteReceiverConnection.getScreenResolutionText()})")
    }

    private fun initViews(view: View) {
        dialogTitle = view.findViewById(R.id.dialog_title)
        closeButton = view.findViewById(R.id.close_button)
        clearScreenButton = view.findViewById(R.id.clear_screen_button)
        saveButton = view.findViewById(R.id.save_button)
        broadcastButton = view.findViewById(R.id.broadcast_button)
        windowButton = view.findViewById(R.id.window_button)
        receiveButton = view.findViewById(R.id.receive_button)
        updateButton = view.findViewById(R.id.update_button)

        // 🪟 初始化窗口容器可视化组件
        windowVisualizationView = view.findViewById(R.id.window_visualization_view)

        // 🎯 设置窗口拖动监听器
        setupWindowDragListener()

        // 计算远程控制窗口的缩放比例
        calculateRemoteControlScale()
    }

    private fun setupClickListeners() {
        closeButton.setOnClickListener {
            dismiss()
        }

        clearScreenButton.setOnClickListener {
            handleClearScreenClick()
        }

        saveButton.setOnClickListener {
            handleSaveClick()
        }

        broadcastButton.setOnClickListener {
            handleBroadcastClick()
        }

        windowButton.setOnClickListener {
            handleWindowClick()
        }

        receiveButton.setOnClickListener {
            handleReceiveClick()
        }

        updateButton.setOnClickListener {
            handleUpdateClick()
        }
    }

    private fun setupUI() {
        // 设置标题
        dialogTitle.text = "远程接收端控制 - ${remoteReceiverConnection.deviceName}"

        // 根据连接状态设置按钮可用性
        val isConnected = remoteReceiverConnection.isConnected
        AppLog.d("🔧 setupUI - 设备连接状态: ${remoteReceiverConnection.deviceName} -> $isConnected")

        clearScreenButton.isEnabled = isConnected
        saveButton.isEnabled = isConnected
        broadcastButton.isEnabled = isConnected
        windowButton.isEnabled = isConnected
        receiveButton.isEnabled = isConnected
        updateButton.isEnabled = isConnected

        // 设置按钮透明度
        val alpha = if (isConnected) 1.0f else 0.5f
        clearScreenButton.alpha = alpha
        saveButton.alpha = alpha
        broadcastButton.alpha = alpha
        windowButton.alpha = alpha
        receiveButton.alpha = alpha
        updateButton.alpha = alpha

        AppLog.d("🔧 按钮状态设置完成 - 启用: $isConnected, 透明度: $alpha")

        if (!isConnected) {
            Toast.makeText(requireContext(), "设备未连接，控制功能不可用", Toast.LENGTH_SHORT).show()
        }
    }

    // ========== 按钮点击事件处理 ==========

    private fun handleClearScreenClick() {
        if (!remoteReceiverConnection.isConnected) return
        
        AppLog.d("清屏按钮被点击")
        Toast.makeText(requireContext(), "清屏功能待实现", Toast.LENGTH_SHORT).show()
        
        // TODO: 实现清屏功能
        // 发送清屏指令到远程设备
    }

    private fun handleSaveClick() {
        if (!remoteReceiverConnection.isConnected) return
        
        AppLog.d("保存按钮被点击")
        Toast.makeText(requireContext(), "保存功能待实现", Toast.LENGTH_SHORT).show()
        
        // TODO: 实现保存功能
        // 发送保存指令到远程设备
    }

    private fun handleBroadcastClick() {
        if (!remoteReceiverConnection.isConnected) return
        
        AppLog.d("导播按钮被点击")
        Toast.makeText(requireContext(), "导播功能待实现", Toast.LENGTH_SHORT).show()
        
        // TODO: 实现导播功能
        // 发送导播指令到远程设备
    }

    private fun handleWindowClick() {
        AppLog.d("🔧 窗口按钮被点击 - 检查连接状态: ${remoteReceiverConnection.isConnected}")

        if (!remoteReceiverConnection.isConnected) {
            AppLog.w("🔧 窗口按钮点击被阻止 - 设备未连接: ${remoteReceiverConnection.deviceName}")
            Toast.makeText(requireContext(), "设备未连接，无法打开窗口管理", Toast.LENGTH_SHORT).show()
            return
        }

        AppLog.d("窗口按钮被点击 - 打开远程投屏窗口管理窗口")

        try {
            // 创建并显示远程窗口管理对话框
            val windowManagerDialog = RemoteWindowManagerDialog.newInstance(remoteReceiverConnection)

            // 🪟 设置窗口信息更新回调，用于同步可视化数据
            // 注意：不再在对话框关闭时清除可视化，保持持久显示
            windowManagerDialog.onDialogDismissed = {
                AppLog.d("【窗口可视化】投屏窗口管理对话框已关闭，保持可视化显示")
            }

            // 🪟 设置窗口信息更新回调
            windowManagerDialog.onWindowInfoUpdated = { windowInfoList, updateMode ->
                AppLog.d("【窗口可视化】收到窗口信息更新回调，更新模式: $updateMode")

                when (updateMode) {
                    WindowUpdateMode.PARTIAL -> {
                        // 部分更新模式：只更新窗口设置窗口的控件参数，不更新可视化窗口
                        AppLog.d("【窗口可视化】部分更新模式，跳过可视化窗口更新")
                    }
                    WindowUpdateMode.FULL -> {
                        // 全量更新模式：更新可视化窗口的变换参数和窗口设置窗口的控件参数
                        updateWindowVisualization(windowInfoList)
                        AppLog.d("【窗口可视化】全量更新模式，已更新可视化窗口")
                    }
                }
            }

            // 🎯 设置裁剪模式控制回调
            windowManagerDialog.onCropModeControl = { connectionId, isEnabled ->
                handleCropModeControl(connectionId, isEnabled)
            }

            windowManagerDialog.show(parentFragmentManager, "RemoteWindowManagerDialog")

            AppLog.d("【远程窗口管理】对话框已显示: ${remoteReceiverConnection.deviceName}")

            // 🪟 自动获取窗口信息并启用可视化
            requestWindowInfoForVisualization()

        } catch (e: Exception) {
            AppLog.e("【远程窗口管理】显示对话框失败", e)
            Toast.makeText(requireContext(), "打开窗口管理失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 🎯 处理裁剪模式控制
     * @param connectionId 窗口连接ID
     * @param isEnabled 是否启用裁剪模式
     */
    private fun handleCropModeControl(connectionId: String, isEnabled: Boolean) {
        AppLog.d("【裁剪模式控制】连接ID: $connectionId, 启用: $isEnabled")

        if (isEnabled) {
            // 进入裁剪模式
            val success = windowVisualizationView.enterCropModeByConnectionId(connectionId)
            if (success) {
                AppLog.d("【裁剪模式控制】成功进入裁剪模式: $connectionId")
            } else {
                AppLog.w("【裁剪模式控制】进入裁剪模式失败: $connectionId")
                Toast.makeText(requireContext(), "进入裁剪模式失败", Toast.LENGTH_SHORT).show()
            }
        } else {
            // 退出裁剪模式
            val success = windowVisualizationView.exitCurrentCropMode(false)
            if (success) {
                AppLog.d("【裁剪模式控制】成功退出裁剪模式: $connectionId")
            } else {
                AppLog.w("【裁剪模式控制】退出裁剪模式失败: $connectionId")
            }
        }
    }

    /**
     * 🎯 更新本地可视化数据中的缩放因子
     * 确保下次缩放时基础值正确
     */
    private fun updateLocalVisualizationScaleFactor(connectionId: String, newScaleFactor: Float) {
        try {
            // 获取当前可视化数据
            val currentData = windowVisualizationView.getVisualizationDataList()

            // 更新指定窗口的缩放因子
            val updatedData = currentData.map { data ->
                if (data.connectionId == connectionId) {
                    data.copy(scaleFactor = newScaleFactor)
                } else {
                    data
                }
            }

            // 更新可视化视图
            windowVisualizationView.updateVisualizationData(updatedData)

            AppLog.d("【窗口缩放】已更新本地可视化数据缩放因子: $connectionId -> $newScaleFactor")
        } catch (e: Exception) {
            AppLog.e("【窗口缩放】更新本地可视化数据缩放因子失败", e)
        }
    }

    private fun handleReceiveClick() {
        AppLog.d("🔧 接收按钮被点击 - 检查连接状态: ${remoteReceiverConnection.isConnected}")

        if (!remoteReceiverConnection.isConnected) {
            AppLog.w("🔧 接收按钮点击被阻止 - 设备未连接: ${remoteReceiverConnection.deviceName}")
            Toast.makeText(requireContext(), "设备未连接，无法打开设置控制", Toast.LENGTH_SHORT).show()
            return
        }

        AppLog.d("接收按钮被点击 - 打开远程接收端设置控制窗口")

        // 打开远程接收端设置控制窗口
        val settingsDialog = RemoteReceiverSettingsControlDialog.newInstance(remoteReceiverConnection)
        settingsDialog.show(parentFragmentManager, "RemoteReceiverSettingsControlDialog")
    }



    private fun handleUpdateClick() {
        AppLog.d("🔄 更新按钮被点击 - 检查连接状态: ${remoteReceiverConnection.isConnected}")

        if (!remoteReceiverConnection.isConnected) {
            AppLog.w("🔄 更新按钮点击被阻止 - 设备未连接: ${remoteReceiverConnection.deviceName}")
            Toast.makeText(requireContext(), "设备未连接，无法更新窗口信息", Toast.LENGTH_SHORT).show()
            return
        }

        AppLog.d("🔄 更新按钮被点击 - 执行FULL模式窗口信息更新并请求文字内容")

        try {
            // 显示更新提示
            Toast.makeText(requireContext(), "正在更新窗口信息...", Toast.LENGTH_SHORT).show()

            // 通过RemoteReceiverManager发送窗口管理请求
            val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()

            // 注册临时处理器以接收响应并执行FULL模式更新
            manager.registerWindowManagerDialog(remoteReceiverConnection.id, createTemporaryUpdateHandler())

            // 发送窗口信息请求
            manager.sendWindowManagerRequest(remoteReceiverConnection)

            AppLog.d("🔄 FULL模式窗口信息更新请求已发送到: ${remoteReceiverConnection.deviceName}")

        } catch (e: Exception) {
            AppLog.e("🔄 发送窗口信息更新请求失败", e)
            Toast.makeText(requireContext(), "更新窗口信息失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 计算基于接收端屏幕分辨率的自适应对话框尺寸
     * 🚀 优化：基于接收端分辨率等比缩放，保持宽高比
     */
    private fun calculateAdaptiveDialogSize(): Pair<Int, Int> {
        // 获取遥控端设备的屏幕尺寸
        val localDisplayMetrics = resources.displayMetrics
        val localScreenWidth = localDisplayMetrics.widthPixels
        val localScreenHeight = localDisplayMetrics.heightPixels

        // 📐 严格检查：必须有有效的屏幕分辨率信息
        if (!remoteReceiverConnection.hasValidScreenResolution()) {
            val errorMsg = "📐 无法计算窗口尺寸：缺少有效的接收端屏幕分辨率信息"
            AppLog.e(errorMsg)
            throw IllegalStateException(errorMsg)
        }

        val remoteWidth = remoteReceiverConnection.screenWidth
        val remoteHeight = remoteReceiverConnection.screenHeight

        // 使用更合理的安全区域（留出2%边距）
        val maxWidth = (localScreenWidth * 0.98).toInt()
        val maxHeight = (localScreenHeight * 0.98).toInt()

        // 计算最优缩放比例，充分利用屏幕空间
        val maxWidthScale = maxWidth.toDouble() / remoteWidth
        val maxHeightScale = maxHeight.toDouble() / remoteHeight
        val maxPossibleScale = minOf(maxWidthScale, maxHeightScale)

        // 使用能适配安全区域的最大缩放比例
        val scale = maxPossibleScale

        val targetWidth = (remoteWidth * scale).toInt()
        val targetHeight = (remoteHeight * scale).toInt()

        AppLog.d("📐 自适应窗口尺寸计算:")
        AppLog.d("  接收端分辨率: ${remoteWidth}×${remoteHeight}")
        AppLog.d("  遥控端屏幕: ${localScreenWidth}×${localScreenHeight}")
        AppLog.d("  安全区域: ${maxWidth}×${maxHeight}")
        AppLog.d("  最终缩放比例: ${String.format("%.1f", scale)}倍")
        AppLog.d("  最终窗口尺寸: ${targetWidth}×${targetHeight}")

        return Pair(targetWidth, targetHeight)
    }

    // ========== 🚀 被动连接状态监听（零功耗优化） ==========

    /**
     * 🚀 优化：注册连接状态更新通知（被动监听，零功耗）
     */
    private fun registerForConnectionStateUpdates() {
        val parentFragment = parentFragment
        if (parentFragment is RemoteControlManagerDialog) {
            parentFragment.registerReceiverControlDialog(remoteReceiverConnection.id, this)
            AppLog.d("已注册连接状态更新通知: ${remoteReceiverConnection.deviceName}")
        }

        // 📸 关键修复：同时在RemoteReceiverManager中注册，用于接收截图响应
        val receiverManager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
        receiverManager.registerReceiverControlDialog(remoteReceiverConnection.id, this)
        AppLog.d("📸 已在RemoteReceiverManager中注册对话框: ${remoteReceiverConnection.deviceName}")
    }

    /**
     * 🚀 被动接收连接状态更新（由父对话框主动调用）
     */
    fun onConnectionStateChanged(newReceiver: RemoteReceiverConnection) {
        val currentConnectionState = remoteReceiverConnection.isConnected
        val newConnectionState = newReceiver.isConnected

        if (newConnectionState != currentConnectionState) {
            remoteReceiverConnection = newReceiver
            updateUIForConnectionState(newConnectionState)

            // 🪟 处理连接状态变化的可视化逻辑
            if (newConnectionState) {
                // 连接建立，自动获取窗口信息
                AppLog.d("【窗口可视化】检测到连接建立，自动获取初始窗口信息")
                requestInitialWindowInfoForVisualization()
            } else {
                // 连接断开，清除可视化
                AppLog.d("【窗口可视化】检测到连接断开，清除窗口可视化")
                clearWindowVisualization()
            }

            AppLog.d("接收到连接状态变化通知: ${newReceiver.deviceName} -> $newConnectionState")
        }
    }

    /**
     * 更新UI以反映连接状态变化
     */
    private fun updateUIForConnectionState(isConnected: Boolean) {
        AppLog.d("🔧 updateUIForConnectionState - 更新连接状态: ${remoteReceiverConnection.deviceName} -> $isConnected")

        // 更新按钮可用性
        clearScreenButton.isEnabled = isConnected
        saveButton.isEnabled = isConnected
        broadcastButton.isEnabled = isConnected
        windowButton.isEnabled = isConnected
        receiveButton.isEnabled = isConnected
        updateButton.isEnabled = isConnected

        // 更新按钮透明度
        val alpha = if (isConnected) 1.0f else 0.5f
        clearScreenButton.alpha = alpha
        saveButton.alpha = alpha
        broadcastButton.alpha = alpha
        windowButton.alpha = alpha
        receiveButton.alpha = alpha
        updateButton.alpha = alpha

        AppLog.d("🔧 按钮状态已更新 - 启用: $isConnected, 透明度: $alpha")
        AppLog.d("🔧 窗口按钮状态 - 启用: ${windowButton.isEnabled}, 可点击: ${windowButton.isClickable}")
        AppLog.d("🔧 接收按钮状态 - 启用: ${receiveButton.isEnabled}, 可点击: ${receiveButton.isClickable}")

        // 显示状态提示
        val statusMessage = if (isConnected) {
            "设备已连接，控制功能可用"
        } else {
            "设备连接已断开，控制功能不可用"
        }
        Toast.makeText(requireContext(), statusMessage, Toast.LENGTH_SHORT).show()

        AppLog.d("远程接收端控制对话框UI已更新: ${remoteReceiverConnection.deviceName}, 连接状态: $isConnected")
    }

    override fun onDestroy() {
        super.onDestroy()
        // 🚀 自动注销连接状态更新通知
        val parentFragment = parentFragment
        if (parentFragment is RemoteControlManagerDialog) {
            parentFragment.unregisterReceiverControlDialog(remoteReceiverConnection.id)
            AppLog.d("已注销连接状态更新通知: ${remoteReceiverConnection.deviceName}")
        }

        // 📸 关键修复：从RemoteReceiverManager中注销对话框
        try {
            val receiverManager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            receiverManager.unregisterReceiverControlDialog(remoteReceiverConnection.id)
            AppLog.d("📸 已从RemoteReceiverManager中注销对话框: ${remoteReceiverConnection.deviceName}")
        } catch (e: Exception) {
            AppLog.w("📸 从RemoteReceiverManager注销对话框失败", e)
        }

        // 🪟 清理窗口可视化资源（只在对话框销毁时清除）
        clearWindowVisualization()

        // 🪟 确保注销临时窗口管理处理器
        try {
            val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            manager.unregisterWindowManagerDialog(remoteReceiverConnection.id)
            AppLog.d("【窗口可视化】已注销临时窗口管理处理器")
        } catch (e: Exception) {
            AppLog.w("【窗口可视化】注销临时窗口管理处理器失败", e)
        }
    }

    // ========== 🪟 窗口容器可视化功能 ==========

    /**
     * 计算远程控制窗口的缩放比例
     * 🔧 修复：直接基于实际窗口尺寸计算缩放比例，确保100%一致性
     */
    private fun calculateRemoteControlScale() {
        if (!remoteReceiverConnection.hasValidScreenResolution()) {
            AppLog.w("【窗口可视化】接收端屏幕分辨率信息无效，使用默认缩放比例")
            remoteControlScale = 1.0
            return
        }

        // 🔧 最佳修复：直接基于实际窗口尺寸计算缩放比例
        val (actualDialogWidth, actualDialogHeight) = calculateAdaptiveDialogSize()
        val remoteWidth = remoteReceiverConnection.screenWidth
        val remoteHeight = remoteReceiverConnection.screenHeight

        // 计算实际的缩放比例（基于实际窗口尺寸）
        val widthScale = actualDialogWidth.toDouble() / remoteWidth
        val heightScale = actualDialogHeight.toDouble() / remoteHeight
        remoteControlScale = minOf(widthScale, heightScale)

        AppLog.d("【窗口可视化】远程控制窗口缩放比例计算（基于实际窗口尺寸）:")
        AppLog.d("  接收端屏幕: ${remoteWidth}×${remoteHeight}")
        AppLog.d("  实际窗口尺寸: ${actualDialogWidth}×${actualDialogHeight}")
        AppLog.d("  宽度缩放: ${"%.3f".format(widthScale)}, 高度缩放: ${"%.3f".format(heightScale)}")
        AppLog.d("  最终缩放比例: ${"%.3f".format(remoteControlScale)}")

        // 🔍 验证计算结果
        val expectedWidth = (remoteWidth * remoteControlScale).toInt()
        val expectedHeight = (remoteHeight * remoteControlScale).toInt()
        AppLog.d("  验证：预期窗口尺寸 ${expectedWidth}×${expectedHeight} vs 实际窗口尺寸 ${actualDialogWidth}×${actualDialogHeight}")
    }

    /**
     * 📝 获取窗口可视化视图
     */
    fun getWindowVisualizationView(): com.example.castapp.ui.view.WindowContainerVisualizationView? {
        return if (::windowVisualizationView.isInitialized) {
            windowVisualizationView
        } else {
            AppLog.w("【远程控制对话框】窗口可视化视图未初始化")
            null
        }
    }

    /**
     * 🪟 请求初始窗口信息用于可视化（远程控制窗口创建时调用）
     */
    private fun requestInitialWindowInfoForVisualization() {
        AppLog.d("【窗口可视化】请求初始窗口信息用于可视化")

        try {
            // 通过RemoteReceiverManager发送窗口管理请求
            val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()

            // 注册自己为窗口管理对话框，以便接收响应
            manager.registerWindowManagerDialog(remoteReceiverConnection.id, createTemporaryWindowManagerHandler())

            // 发送窗口信息请求
            manager.sendWindowManagerRequest(remoteReceiverConnection)

            AppLog.d("【窗口可视化】初始窗口信息请求已发送到: ${remoteReceiverConnection.deviceName}")

        } catch (e: Exception) {
            AppLog.e("【窗口可视化】发送初始窗口信息请求失败", e)
            Toast.makeText(requireContext(), "获取初始窗口信息失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 🪟 请求窗口信息用于可视化（点击窗口按钮时调用）
     */
    private fun requestWindowInfoForVisualization() {
        AppLog.d("【窗口可视化】请求窗口信息用于实时更新")

        try {
            // 通过RemoteReceiverManager发送窗口管理请求
            val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            manager.sendWindowManagerRequest(remoteReceiverConnection)

            AppLog.d("【窗口可视化】实时更新窗口信息请求已发送到: ${remoteReceiverConnection.deviceName}")

        } catch (e: Exception) {
            AppLog.e("【窗口可视化】发送实时更新窗口信息请求失败", e)
            Toast.makeText(requireContext(), "获取窗口信息失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 更新窗口容器可视化
     */
    fun updateWindowVisualization(windowInfoList: List<com.example.castapp.model.CastWindowInfo>) {
        if (!::windowVisualizationView.isInitialized) {
            AppLog.w("【窗口可视化】可视化组件未初始化")
            return
        }

        AppLog.d("【窗口可视化】更新窗口可视化数据: ${windowInfoList.size} 个窗口")

        // 🔧 确保在UI线程中执行UI更新操作
        activity?.runOnUiThread {
            try {
                // 计算可视化数据
                val visualizationDataList = WindowScaleCalculator.calculateWindowVisualizationData(
                    windowInfoList = windowInfoList,
                    remoteControlScale = remoteControlScale
                )

                // 更新可视化组件
                windowVisualizationView.updateVisualizationData(visualizationDataList)
                windowVisualizationView.visibility = if (visualizationDataList.isNotEmpty()) View.VISIBLE else View.GONE

                AppLog.d("【窗口可视化】可视化更新完成: ${visualizationDataList.size} 个窗口容器")

                // 🎯 层级修复：记录层级信息用于调试
                if (visualizationDataList.isNotEmpty()) {
                    AppLog.d("【层级修复】窗口层级信息:")
                    visualizationDataList.sortedBy { it.zOrder }.forEachIndexed { index, data ->
                        AppLog.d("  层级${data.zOrder}: ${data.getShortConnectionId()} (索引$index)")
                    }
                }

                // 📸📝 窗口信息更新完成后，根据窗口类型选择性请求截图和文字内容
                if (windowInfoList.isNotEmpty()) {
                    // 🎯 关键修复：根据窗口类型选择性请求数据
                    val hasNonTextWindows = windowInfoList.any { !it.connectionId.startsWith("text_") }
                    val hasTextWindows = windowInfoList.any { it.connectionId.startsWith("text_") }

                    if (hasNonTextWindows) {
                        // 只有非文字窗口才请求截图
                        requestScreenshotForVisualization()
                        AppLog.d("📸 检测到非文字窗口，请求截图")
                    } else {
                        AppLog.d("📸 只有文字窗口，跳过截图请求")
                    }

                    if (hasTextWindows) {
                        // 只有文字窗口才请求文字内容
                        requestTextContentForVisualization()
                        AppLog.d("📝 检测到文字窗口，请求文字内容")
                    } else {
                        AppLog.d("📝 无文字窗口，跳过文字内容请求")
                    }
                }

            } catch (e: Exception) {
                AppLog.e("【窗口可视化】更新可视化失败", e)
            }
        } ?: run {
            AppLog.w("【窗口可视化】无activity上下文，无法更新UI")
        }
    }

    /**
     * 🪟 创建临时窗口管理处理器，用于接收初始窗口信息响应
     */
    private fun createTemporaryWindowManagerHandler(): RemoteWindowManagerDialog {
        AppLog.d("【窗口可视化】创建临时窗口管理处理器")

        // 创建一个临时的RemoteWindowManagerDialog实例，仅用于处理消息
        val tempDialog = RemoteWindowManagerDialog(remoteReceiverConnection)

        // 设置窗口信息更新回调
        tempDialog.onWindowInfoUpdated = { windowInfoList, updateMode ->
            AppLog.d("【窗口可视化】临时处理器收到窗口信息更新回调: ${windowInfoList.size} 个窗口，更新模式: $updateMode")

            // 🔧 临时处理器始终进行全量更新（用于初始化可视化）
            updateWindowVisualization(windowInfoList)
            AppLog.d("【窗口可视化】通过临时处理器更新初始可视化数据完成")

            // 处理完成后注销临时处理器
            try {
                val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
                manager.unregisterWindowManagerDialog(remoteReceiverConnection.id)
                AppLog.d("【窗口可视化】临时处理器已注销")
            } catch (e: Exception) {
                AppLog.w("【窗口可视化】注销临时处理器失败", e)
            }
        }

        AppLog.d("【窗口可视化】临时窗口管理处理器创建完成")
        return tempDialog
    }

    /**
     * 🔄 创建临时更新处理器，用于处理更新按钮的FULL模式窗口信息响应
     */
    private fun createTemporaryUpdateHandler(): RemoteWindowManagerDialog {
        AppLog.d("🔄 创建临时更新处理器（FULL模式）")

        // 创建一个临时的RemoteWindowManagerDialog实例，仅用于处理更新消息
        val tempDialog = RemoteWindowManagerDialog(remoteReceiverConnection)

        // 设置窗口信息更新回调
        tempDialog.onWindowInfoUpdated = { windowInfoList, updateMode ->
            AppLog.d("🔄 临时更新处理器收到窗口信息更新回调: ${windowInfoList.size} 个窗口，更新模式: $updateMode")

            // 🔧 执行FULL模式全量更新：同时更新可视化窗口和缓存窗口信息
            updateWindowVisualization(windowInfoList)

            // 缓存窗口信息，供窗口设置窗口使用
            cachedWindowInfoList = windowInfoList

            AppLog.d("🔄 FULL模式全量更新完成：可视化窗口已更新，窗口信息已缓存")

            // 🎯 修复：在UI线程中显示Toast
            activity?.runOnUiThread {
                Toast.makeText(requireContext(), "窗口信息更新完成", Toast.LENGTH_SHORT).show()
            }

            // 处理完成后注销临时处理器
            try {
                val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
                manager.unregisterWindowManagerDialog(remoteReceiverConnection.id)
                AppLog.d("🔄 临时更新处理器已注销")
            } catch (e: Exception) {
                AppLog.w("🔄 注销临时更新处理器失败", e)
            }
        }

        AppLog.d("🔄 临时更新处理器创建完成")
        return tempDialog
    }

    /**
     * 📸 请求截图用于可视化显示
     */
    private fun requestScreenshotForVisualization() {
        if (!remoteReceiverConnection.isConnected) {
            AppLog.w("📸 设备未连接，无法请求截图: ${remoteReceiverConnection.deviceName}")
            return
        }

        AppLog.d("📸 窗口信息更新完成，自动触发截图请求")

        try {
            // 通过RemoteReceiverManager发送截图请求
            val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            manager.sendScreenshotRequest(remoteReceiverConnection)

            AppLog.d("📸 自动截图请求已发送到: ${remoteReceiverConnection.deviceName}")

        } catch (e: Exception) {
            AppLog.e("📸 发送自动截图请求失败", e)
            // 不显示Toast，避免干扰用户体验
        }
    }

    /**
     * 📝 请求文字内容用于可视化显示
     */
    private fun requestTextContentForVisualization() {
        if (!remoteReceiverConnection.isConnected) {
            AppLog.w("📝 设备未连接，无法请求文字内容: ${remoteReceiverConnection.deviceName}")
            return
        }

        AppLog.d("📝 窗口信息更新完成，自动触发文字内容请求")

        try {
            // 通过RemoteReceiverManager发送文字内容请求
            val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            manager.sendTextContentRequest(remoteReceiverConnection)

            AppLog.d("📝 自动文字内容请求已发送到: ${remoteReceiverConnection.deviceName}")

        } catch (e: Exception) {
            AppLog.e("📝 发送自动文字内容请求失败", e)
            // 不显示Toast，避免干扰用户体验
        }
    }

    // ========== 📸 截图功能处理方法 ==========

    /**
     * 📸 处理截图响应消息
     */
    fun handleScreenshotResponse(message: com.example.castapp.websocket.ControlMessage) {
        try {
            AppLog.d("📸 [${remoteReceiverConnection.deviceName}] 收到截图响应消息")
            AppLog.d("📸 消息数据: ${message.data}")

            // 解析截图数据 - 使用安全的类型转换
            @Suppress("UNCHECKED_CAST")
            val screenshotsData = message.data["screenshots"] as? List<Map<String, Any>>
            if (screenshotsData.isNullOrEmpty()) {
                AppLog.w("📸 截图数据为空，消息内容: ${message.data}")
                activity?.runOnUiThread {
                    Toast.makeText(requireContext(), "截图数据为空", Toast.LENGTH_SHORT).show()
                }
                return
            }

            AppLog.d("📸 解析到 ${screenshotsData.size} 个截图数据")
            screenshotsData.forEachIndexed { index, data ->
                val connectionId = data["connectionId"] as? String
                val hasScreenshotData = data.containsKey("imageData")
                AppLog.d("📸 截图[$index]: connectionId=$connectionId, hasData=$hasScreenshotData")
            }

            // 在UI线程中更新截图显示
            activity?.runOnUiThread {
                try {
                    AppLog.d("📸 开始更新截图可视化显示")
                    updateScreenshotVisualization(screenshotsData)
                    Toast.makeText(requireContext(), "截图获取成功", Toast.LENGTH_SHORT).show()
                    AppLog.d("📸 截图可视化显示更新完成")
                } catch (e: Exception) {
                    AppLog.e("📸 更新截图显示失败", e)
                    Toast.makeText(requireContext(), "截图显示失败: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }

        } catch (e: Exception) {
            AppLog.e("📸 处理截图响应失败", e)
            activity?.runOnUiThread {
                Toast.makeText(requireContext(), "截图处理失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 📸 处理截图错误消息
     */
    fun handleScreenshotError(errorMessage: String) {
        AppLog.e("📸 截图失败: $errorMessage")
        activity?.runOnUiThread {
            Toast.makeText(requireContext(), "截图失败: $errorMessage", Toast.LENGTH_SHORT).show()
        }
    }





    /**
     * 📸 更新截图可视化显示
     */
    private fun updateScreenshotVisualization(screenshotsData: List<Map<String, Any>>) {
        if (!::windowVisualizationView.isInitialized) {
            AppLog.w("📸 窗口可视化组件未初始化")
            return
        }

        try {
            // 将截图数据传递给可视化组件
            windowVisualizationView.updateScreenshots(screenshotsData)
            AppLog.d("📸 截图可视化更新完成: ${screenshotsData.size} 个截图")

        } catch (e: Exception) {
            AppLog.e("📸 更新截图可视化失败", e)
            throw e
        }
    }

    /**
     * 📝 更新文字内容可视化显示
     */
    private fun updateTextContentVisualization(textContentsData: List<Map<String, Any>>) {
        if (!::windowVisualizationView.isInitialized) {
            AppLog.w("📝 窗口可视化组件未初始化")
            return
        }

        try {
            // 将文字内容数据传递给可视化组件
            windowVisualizationView.updateTextContents(textContentsData)
            AppLog.d("📝 文字内容可视化更新完成: ${textContentsData.size} 个文字内容")

        } catch (e: Exception) {
            AppLog.e("📝 更新文字内容可视化失败", e)
            throw e
        }
    }

    /**
     * 🎯 新增：同步更新遥控端文字窗口的实际内容和样式
     */
    private fun syncRemoteTextWindowsWithReceivedData(textContentsData: List<Map<String, Any>>) {
        try {
            AppLog.d("📝 开始同步遥控端文字窗口数据: ${textContentsData.size} 个窗口")

            if (textContentsData.isEmpty()) {
                AppLog.w("📝 文字内容数据为空，跳过同步")
                return
            }

            var successCount = 0
            var failureCount = 0

            textContentsData.forEach { textData ->
                try {
                    val connectionId = textData["connectionId"] as? String
                    if (connectionId.isNullOrEmpty()) {
                        AppLog.w("📝 文字数据缺少有效的connectionId: $textData")
                        failureCount++
                        return@forEach
                    }

                    // 验证是否为文字窗口
                    if (!connectionId.startsWith("text_")) {
                        AppLog.d("📝 跳过非文字窗口: $connectionId")
                        return@forEach
                    }

                    // 查找对应的遥控端文字窗口
                    val remoteTextWindowView = findRemoteTextWindowView(connectionId)
                    if (remoteTextWindowView != null) {
                        // 应用完整的格式数据到遥控端文字窗口
                        applyCompleteFormatDataToRemoteTextWindow(remoteTextWindowView, textData)
                        successCount++
                        AppLog.d("📝 遥控端文字窗口同步完成: $connectionId")
                    } else {
                        AppLog.w("📝 未找到对应的遥控端文字窗口: $connectionId")
                        failureCount++
                    }

                } catch (e: Exception) {
                    failureCount++
                    AppLog.e("📝 同步单个文字窗口失败: ${textData["connectionId"]}", e)
                }
            }

            AppLog.d("📝 遥控端文字窗口数据同步完成: 成功=$successCount, 失败=$failureCount")

            // 如果有失败的情况，在UI线程中显示警告
            if (failureCount > 0) {
                activity?.runOnUiThread {
                    Toast.makeText(requireContext(), "部分文字窗口同步失败 ($failureCount/${ textContentsData.size})", Toast.LENGTH_SHORT).show()
                }
            }

        } catch (e: Exception) {
            AppLog.e("📝 同步遥控端文字窗口数据失败", e)
            activity?.runOnUiThread {
                Toast.makeText(requireContext(), "文字窗口同步失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 🎯 新增：根据connectionId查找遥控端文字窗口
     */
    private fun findRemoteTextWindowView(connectionId: String): com.example.castapp.ui.view.TextWindowView? {
        try {
            if (!::windowVisualizationView.isInitialized) {
                AppLog.w("📝 窗口可视化组件未初始化，无法查找遥控端文字窗口")
                return null
            }

            // 通过反射获取windowContainerViews
            val windowContainerViewsField = windowVisualizationView::class.java.getDeclaredField("windowContainerViews")
            windowContainerViewsField.isAccessible = true
            @Suppress("UNCHECKED_CAST")
            val windowContainerViews = windowContainerViewsField.get(windowVisualizationView) as? Map<String, com.example.castapp.ui.view.WindowVisualizationContainerView>

            if (windowContainerViews != null) {
                val containerView = windowContainerViews[connectionId]
                if (containerView != null) {
                    // 在容器中查找TextWindowView
                    for (i in 0 until containerView.childCount) {
                        val child = containerView.getChildAt(i)
                        if (child is com.example.castapp.ui.view.TextWindowView) {
                            AppLog.d("📝 找到遥控端文字窗口: $connectionId")
                            return child
                        }
                    }
                    AppLog.w("📝 容器中未找到TextWindowView: $connectionId")
                } else {
                    AppLog.w("📝 未找到对应的窗口容器: $connectionId")
                }
            } else {
                AppLog.w("📝 无法获取windowContainerViews")
            }

        } catch (e: Exception) {
            AppLog.e("📝 查找遥控端文字窗口失败: $connectionId", e)
        }

        return null
    }

    /**
     * 🎯 新增：将完整格式数据应用到遥控端文字窗口
     */
    private fun applyCompleteFormatDataToRemoteTextWindow(
        textWindowView: com.example.castapp.ui.view.TextWindowView,
        formatData: Map<String, Any>
    ) {
        try {
            val connectionId = formatData["connectionId"] as? String ?: "unknown"
            AppLog.d("📝 开始应用完整格式数据到遥控端文字窗口: $connectionId")

            // 获取基本数据
            val textContent = formatData["textContent"] as? String ?: ""
            val richTextData = formatData["richTextData"] as? String
            val isBold = formatData["isBold"] as? Boolean ?: false
            val isItalic = formatData["isItalic"] as? Boolean ?: false
            val fontSize = formatData["fontSize"] as? Int ?: 13

            AppLog.d("📝 基本格式数据: 内容='$textContent', 富文本=${richTextData != null}, 加粗=$isBold, 倾斜=$isItalic, 字号=${fontSize}sp")

            // 优先应用富文本格式
            if (richTextData != null && textContent.isNotEmpty()) {
                try {
                    val textFormatManager = com.example.castapp.utils.TextFormatManager(requireContext())
                    val spannableString = textFormatManager.deserializeSpannableString(textContent, richTextData)
                    textWindowView.setText(spannableString)
                    AppLog.d("📝 富文本格式已应用到遥控端文字窗口: $connectionId")
                } catch (e: Exception) {
                    AppLog.e("📝 应用富文本格式失败，使用基本格式: $connectionId", e)
                    applyBasicFormatToRemoteTextWindow(textWindowView, textContent, isBold, isItalic, fontSize)
                }
            } else {
                // 使用基本格式
                applyBasicFormatToRemoteTextWindow(textWindowView, textContent, isBold, isItalic, fontSize)
            }

            // 应用扩展格式
            applyExtendedFormatToRemoteTextWindow(textWindowView, formatData)

            AppLog.d("📝 完整格式数据应用完成: $connectionId")

        } catch (e: Exception) {
            AppLog.e("📝 应用完整格式数据失败", e)
        }
    }

    /**
     * 🎯 新增：应用基本格式到遥控端文字窗口
     */
    private fun applyBasicFormatToRemoteTextWindow(
        textWindowView: com.example.castapp.ui.view.TextWindowView,
        textContent: String,
        isBold: Boolean,
        isItalic: Boolean,
        fontSize: Int
    ) {
        try {
            // 设置文本内容
            textWindowView.setTextContent(textContent)

            // 应用格式到整个文本
            textWindowView.applyFormatToAllText(isBold, isItalic, fontSize)

            AppLog.d("📝 基本格式已应用: 内容='$textContent', 加粗=$isBold, 倾斜=$isItalic, 字号=${fontSize}sp")

        } catch (e: Exception) {
            AppLog.e("📝 应用基本格式失败", e)
        }
    }

    /**
     * 🎯 新增：应用扩展格式到遥控端文字窗口
     */
    private fun applyExtendedFormatToRemoteTextWindow(
        textWindowView: com.example.castapp.ui.view.TextWindowView,
        formatData: Map<String, Any>
    ) {
        try {
            // 应用行间距
            val lineSpacing = formatData["lineSpacing"] as? Float ?: 0.0f
            if (lineSpacing > 0.0f) {
                val lineSpacingExtra = lineSpacing * textWindowView.resources.displayMetrics.density
                textWindowView.setLineSpacing(lineSpacingExtra, 1.0f)
                AppLog.d("📝 行间距已应用: ${lineSpacing}dp")
            }

            // 应用文本对齐
            val textAlignment = formatData["textAlignment"] as? Int
            if (textAlignment != null) {
                textWindowView.gravity = textAlignment
                AppLog.d("📝 文本对齐已应用: $textAlignment")
            }

            // 应用窗口背景颜色
            val isWindowColorEnabled = formatData["isWindowColorEnabled"] as? Boolean ?: false
            val windowBackgroundColor = formatData["windowBackgroundColor"] as? Int ?: android.graphics.Color.TRANSPARENT
            textWindowView.setWindowBackgroundColor(isWindowColorEnabled, windowBackgroundColor)
            AppLog.d("📝 窗口背景颜色已应用: 启用=$isWindowColorEnabled, 颜色=${String.format("#%08X", windowBackgroundColor)}")

        } catch (e: Exception) {
            AppLog.e("📝 应用扩展格式失败", e)
        }
    }

    // ========== 📝 文字内容功能处理方法 ==========

    /**
     * 📝 处理文字内容响应消息
     */
    fun handleTextContentResponse(message: com.example.castapp.websocket.ControlMessage) {
        try {
            AppLog.d("📝 [${remoteReceiverConnection.deviceName}] 收到文字内容响应消息")
            AppLog.d("📝 消息数据: ${message.data}")

            // 解析文字内容数据 - 使用安全的类型转换
            @Suppress("UNCHECKED_CAST")
            val textContentsData = message.data["text_contents"] as? List<Map<String, Any>>
            if (textContentsData.isNullOrEmpty()) {
                AppLog.w("📝 文字内容数据为空，消息内容: ${message.data}")
                activity?.runOnUiThread {
                    Toast.makeText(requireContext(), "文字内容数据为空", Toast.LENGTH_SHORT).show()
                }
                return
            }

            AppLog.d("📝 解析到 ${textContentsData.size} 个文字内容数据")
            textContentsData.forEachIndexed { index, data ->
                val connectionId = data["connectionId"] as? String
                val textContent = data["textContent"] as? String
                AppLog.d("📝 文字内容[$index]: connectionId=$connectionId, 内容=$textContent")
            }

            // 在UI线程中更新文字内容显示
            activity?.runOnUiThread {
                try {
                    AppLog.d("📝 开始更新文字内容可视化显示")
                    updateTextContentVisualization(textContentsData)

                    // 🎯 新增：同步更新遥控端文字窗口的实际内容和样式
                    syncRemoteTextWindowsWithReceivedData(textContentsData)

                    Toast.makeText(requireContext(), "文字内容获取成功", Toast.LENGTH_SHORT).show()
                    AppLog.d("📝 文字内容可视化显示更新完成")
                } catch (e: Exception) {
                    AppLog.e("📝 更新文字内容显示失败", e)
                    Toast.makeText(requireContext(), "文字内容显示失败: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }

        } catch (e: Exception) {
            AppLog.e("📝 处理文字内容响应失败", e)
            activity?.runOnUiThread {
                Toast.makeText(requireContext(), "处理文字内容响应失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 📝 处理文字内容错误消息
     */
    fun handleTextContentError(errorMessage: String) {
        AppLog.e("📝 [${remoteReceiverConnection.deviceName}] 文字内容获取失败: $errorMessage")
        activity?.runOnUiThread {
            Toast.makeText(requireContext(), "文字内容获取失败: $errorMessage", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 清除窗口容器可视化
     */
    private fun clearWindowVisualization() {
        if (::windowVisualizationView.isInitialized) {
            windowVisualizationView.clearVisualization()
            windowVisualizationView.visibility = View.GONE
            AppLog.d("【窗口可视化】已清除窗口容器可视化")
        }
    }

    // 🎯 拖动状态保存
    private var dragStartVisualizedX: Float = 0f
    private var dragStartVisualizedY: Float = 0f

    /**
     * 🎯 设置窗口拖动监听器
     */
    private fun setupWindowDragListener() {
        windowVisualizationView.setOnWindowDragListener(object : WindowContainerVisualizationView.OnWindowDragListener {
            override fun onDragStart(windowData: WindowVisualizationData) {
                // 🎯 关键修复：保存拖动开始时的初始位置
                dragStartVisualizedX = windowData.visualizedX
                dragStartVisualizedY = windowData.visualizedY
                AppLog.d("【窗口拖动】开始拖动窗口: ${windowData.getShortConnectionId()}")
                AppLog.d("  初始可视化位置: ($dragStartVisualizedX, $dragStartVisualizedY)")
            }

            override fun onDragMove(windowData: WindowVisualizationData, newX: Float, newY: Float) {
                // 拖动过程中可以添加实时反馈，但不发送WebSocket消息（避免过度发送）
                AppLog.v("【窗口拖动】拖动中: ${windowData.getShortConnectionId()}, 位置: ($newX, $newY)")
            }

            override fun onDragEnd(windowData: WindowVisualizationData, finalX: Float, finalY: Float) {
                AppLog.d("【窗口拖动】拖动结束: ${windowData.getShortConnectionId()}, 最终位置: ($finalX, $finalY)")

                // 🎯 详细调试：分析窗口数据和坐标类型
                AppLog.d("【窗口拖动调试】窗口数据分析:")
                AppLog.d("  原始位置: (${windowData.originalX}, ${windowData.originalY})")
                AppLog.d("  可视化位置: (${windowData.visualizedX}, ${windowData.visualizedY})")
                AppLog.d("  是否裁剪: ${windowData.isCropping}")
                windowData.cropRectRatio?.let { cropRatio ->
                    AppLog.d("  裁剪比例: left=${cropRatio.left}, top=${cropRatio.top}, right=${cropRatio.right}, bottom=${cropRatio.bottom}")
                }
                AppLog.d("  远程控制缩放: $remoteControlScale")

                // 检查同步开关状态
                val isSyncEnabled = isSyncControlEnabled()
                AppLog.d("【窗口拖动】同步开关状态: $isSyncEnabled")

                if (isSyncEnabled) {
                    // 🎯 关键修复：根据窗口类型采用不同的坐标转换策略
                    val isTextWindow = windowData.connectionId.startsWith("text_")
                    AppLog.d("【坐标转换分析】开始分析坐标转换:")
                    AppLog.d("  窗口类型: ${if (isTextWindow) "文字窗口" else "其他窗口"}")
                    AppLog.d("  遥控端拖动最终位置: ($finalX, $finalY)")

                    val actualX: Float
                    val actualY: Float

                    if (isTextWindow) {
                        // 📝 文字窗口：直接转换最终位置，不使用偏移量
                        // 🎯 关键修复：直接将遥控端的最终位置转换为接收端位置
                        val (convertedX, convertedY) = WindowScaleCalculator.convertRemoteToActualCoordinates(
                            remoteX = finalX,
                            remoteY = finalY,
                            remoteControlScale = remoteControlScale
                        )
                        actualX = convertedX
                        actualY = convertedY

                        AppLog.d("  📝 文字窗口直接位置转换:")
                        AppLog.d("    遥控端最终位置: ($finalX, $finalY)")
                        AppLog.d("    远程控制缩放: $remoteControlScale")
                        AppLog.d("    接收端屏幕位置: ($actualX, $actualY)")
                        AppLog.d("    转换公式: 接收端位置 = 遥控端位置 ÷ 缩放比例")
                    } else {
                        // 🖼️ 其他窗口：使用原有的直接坐标转换
                        val (convertedX, convertedY) = WindowScaleCalculator.convertRemoteToActualCoordinates(
                            remoteX = finalX,
                            remoteY = finalY,
                            remoteControlScale = remoteControlScale
                        )
                        actualX = convertedX
                        actualY = convertedY

                        AppLog.d("  🖼️ 其他窗口直接转换: ($actualX, $actualY)")
                    }

                    AppLog.d("【坐标转换分析】转换结果:")
                    AppLog.d("  接收端屏幕坐标: ($actualX, $actualY)")

                    // 发送窗口位置更新消息到接收端
                    sendWindowPositionUpdate(windowData.connectionId, actualX, actualY)
                } else {
                    AppLog.d("【窗口拖动】同步开关未开启，跳过发送位置更新消息")
                    Toast.makeText(requireContext(), "请先在窗口管理中开启同步开关", Toast.LENGTH_SHORT).show()
                }
            }

            // 🎯 缩放回调实现
            override fun onScaleStart(windowData: WindowVisualizationData) {
                AppLog.d("【窗口缩放】开始缩放窗口: ${windowData.getShortConnectionId()}")
            }

            override fun onScaleMove(windowData: WindowVisualizationData, scaleFactor: Float) {
                // 缩放过程中可以添加实时反馈，但不发送WebSocket消息（避免过度发送）
                AppLog.v("【窗口缩放】缩放中: ${windowData.getShortConnectionId()}, 因子: $scaleFactor")
            }

            override fun onScaleEnd(windowData: WindowVisualizationData, finalScaleFactor: Float, finalX: Float, finalY: Float) {
                AppLog.d("【窗口缩放】缩放结束: ${windowData.getShortConnectionId()}, 绝对缩放因子: $finalScaleFactor")
                AppLog.d("【窗口缩放】最终位置: ($finalX, $finalY)")

                // 检查同步开关状态
                val isSyncEnabled = isSyncControlEnabled()
                AppLog.d("【窗口缩放】同步开关状态: $isSyncEnabled")

                if (isSyncEnabled) {
                    // 🎯 增强型同步：同时发送缩放和位置更新消息
                    sendWindowScaleAndPositionUpdate(windowData.connectionId, finalScaleFactor, finalX, finalY)

                    // 🎯 关键修复：立即更新本地可视化数据中的缩放因子，确保下次缩放时基础值正确
                    updateLocalVisualizationScaleFactor(windowData.connectionId, finalScaleFactor)
                } else {
                    AppLog.d("【窗口缩放】同步开关未开启，跳过发送缩放更新消息")
                    Toast.makeText(requireContext(), "请先在窗口管理中开启同步开关", Toast.LENGTH_SHORT).show()
                }
            }

            // 🎯 旋转回调实现
            override fun onRotationStart(windowData: WindowVisualizationData) {
                AppLog.d("【窗口旋转】开始旋转窗口: ${windowData.getShortConnectionId()}")
            }

            override fun onRotationMove(windowData: WindowVisualizationData, rotationAngle: Float) {
                // 旋转过程中可以添加实时反馈，但不发送WebSocket消息（避免过度发送）
                AppLog.v("【窗口旋转】旋转中: ${windowData.getShortConnectionId()}, 角度: $rotationAngle")
            }

            override fun onRotationEnd(windowData: WindowVisualizationData, finalRotationAngle: Float, finalX: Float, finalY: Float) {
                AppLog.d("【窗口旋转】旋转结束: ${windowData.getShortConnectionId()}, 绝对旋转角度: $finalRotationAngle")
                AppLog.d("【窗口旋转】最终位置: ($finalX, $finalY)")

                // 检查同步开关状态
                val isSyncEnabled = isSyncControlEnabled()
                AppLog.d("【窗口旋转】同步开关状态: $isSyncEnabled")

                if (isSyncEnabled) {
                    // 🎯 增强型同步：同时发送旋转和位置更新消息
                    sendWindowRotationAndPositionUpdate(windowData.connectionId, finalRotationAngle, finalX, finalY)
                } else {
                    AppLog.d("【窗口旋转】同步开关未开启，跳过发送旋转更新消息")
                    Toast.makeText(requireContext(), "请先在窗口管理中开启同步开关", Toast.LENGTH_SHORT).show()
                }
            }

            // 🎯 裁剪回调实现（双击功能已移除）

            override fun onCropModeStart(windowData: WindowVisualizationData) {
                AppLog.d("【窗口裁剪】裁剪模式开始: ${windowData.getShortConnectionId()}")
            }

            override fun onCropAreaChange(windowData: WindowVisualizationData, cropRatio: RectF) {
                // 裁剪过程中可以添加实时反馈，但不发送WebSocket消息（避免过度发送）
                AppLog.v("【窗口裁剪】裁剪区域变化: ${windowData.getShortConnectionId()}, 比例: $cropRatio")
            }

            override fun onCropModeEnd(windowData: WindowVisualizationData, finalCropRatio: RectF?, isCancel: Boolean) {
                AppLog.d("【窗口裁剪】裁剪模式结束: ${windowData.getShortConnectionId()}, 取消: $isCancel")

                if (!isCancel && finalCropRatio != null) {
                    // 检查同步开关状态
                    val isSyncEnabled = isSyncControlEnabled()
                    AppLog.d("【窗口裁剪】同步开关状态: $isSyncEnabled")

                    if (isSyncEnabled) {
                        // 发送窗口裁剪更新消息到接收端
                        sendWindowCropUpdate(windowData.connectionId, finalCropRatio)
                    } else {
                        AppLog.d("【窗口裁剪】同步开关未开启，跳过发送裁剪更新消息")
                        Toast.makeText(requireContext(), "请先在窗口管理中开启同步开关", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    AppLog.d("【窗口裁剪】取消裁剪或无裁剪区域，不发送消息")
                }
            }
        })

        AppLog.d("【窗口拖动、缩放、旋转和裁剪】监听器设置完成")
    }

    /**
     * 🎯 检查同步开关状态
     */
    private fun isSyncControlEnabled(): Boolean {
        val sharedPrefs = requireContext().getSharedPreferences("remote_window_settings", Context.MODE_PRIVATE)
        val syncStateKey = "sync_enabled_${remoteReceiverConnection.id}"
        return sharedPrefs.getBoolean(syncStateKey, false)
    }

    /**
     * 🎯 发送窗口位置更新消息到接收端
     */
    private fun sendWindowPositionUpdate(connectionId: String, x: Float, y: Float) {
        if (!remoteReceiverConnection.isConnected) {
            AppLog.w("【窗口拖动】设备未连接，无法发送位置更新: ${remoteReceiverConnection.deviceName}")
            return
        }

        try {
            // 创建窗口变换控制消息
            val transformData = mapOf(
                "x" to x,
                "y" to y
            )

            val message = ControlMessage.createRemoteWindowTransformControl(
                connectionId = remoteReceiverConnection.id,
                targetWindowId = connectionId,
                transformType = "position",
                transformData = transformData
            )

            // 通过RemoteReceiverManager发送消息
            val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            manager.sendWindowTransformControl(remoteReceiverConnection, message)

            AppLog.d("【窗口拖动】位置更新消息已发送: $connectionId -> ($x, $y)")

        } catch (e: Exception) {
            AppLog.e("【窗口拖动】发送位置更新消息失败: $connectionId", e)
            Toast.makeText(requireContext(), "更新窗口位置失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 🎯 增强型同步：发送窗口缩放和位置组合更新消息到接收端
     */
    private fun sendWindowScaleAndPositionUpdate(connectionId: String, scaleFactor: Float, x: Float, y: Float) {
        if (!remoteReceiverConnection.isConnected) {
            AppLog.w("【窗口缩放】设备未连接，无法发送缩放和位置更新: ${remoteReceiverConnection.deviceName}")
            return
        }

        try {
            // 将遥控端坐标转换为接收端实际屏幕坐标
            val (actualX, actualY) = WindowScaleCalculator.convertRemoteToActualCoordinates(
                remoteX = x,
                remoteY = y,
                remoteControlScale = remoteControlScale
            )

            // 创建组合变换控制消息
            val transformData = mapOf(
                "scale_factor" to scaleFactor,
                "x" to actualX,
                "y" to actualY
            )

            val message = ControlMessage.createRemoteWindowTransformControl(
                connectionId = remoteReceiverConnection.id,
                targetWindowId = connectionId,
                transformType = "scale_and_position",
                transformData = transformData
            )

            // 通过RemoteReceiverManager发送消息
            val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            manager.sendWindowTransformControl(remoteReceiverConnection, message)

            AppLog.d("【窗口缩放】缩放和位置组合更新消息已发送: $connectionId -> Scale=$scaleFactor, Position=($actualX, $actualY)")

        } catch (e: Exception) {
            AppLog.e("【窗口缩放】发送缩放和位置更新消息失败", e)
            Toast.makeText(requireContext(), "更新窗口缩放和位置失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 🎯 增强型同步：发送窗口旋转和位置组合更新消息到接收端
     */
    private fun sendWindowRotationAndPositionUpdate(connectionId: String, rotationAngle: Float, x: Float, y: Float) {
        if (!remoteReceiverConnection.isConnected) {
            AppLog.w("【窗口旋转】设备未连接，无法发送旋转和位置更新: ${remoteReceiverConnection.deviceName}")
            return
        }

        try {
            // 将遥控端坐标转换为接收端实际屏幕坐标
            val (actualX, actualY) = WindowScaleCalculator.convertRemoteToActualCoordinates(
                remoteX = x,
                remoteY = y,
                remoteControlScale = remoteControlScale
            )

            // 创建组合变换控制消息
            val transformData = mapOf(
                "rotation_angle" to rotationAngle,
                "x" to actualX,
                "y" to actualY
            )

            val message = ControlMessage.createRemoteWindowTransformControl(
                connectionId = remoteReceiverConnection.id,
                targetWindowId = connectionId,
                transformType = "rotation_and_position",
                transformData = transformData
            )

            // 通过RemoteReceiverManager发送消息
            val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            manager.sendWindowTransformControl(remoteReceiverConnection, message)

            AppLog.d("【窗口旋转】旋转和位置组合更新消息已发送: $connectionId -> Rotation=$rotationAngle°, Position=($actualX, $actualY)")

        } catch (e: Exception) {
            AppLog.e("【窗口旋转】发送旋转和位置更新消息失败", e)
            Toast.makeText(requireContext(), "更新窗口旋转和位置失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 🎯 发送窗口裁剪更新消息到接收端
     */
    private fun sendWindowCropUpdate(connectionId: String, cropRatio: RectF) {
        if (!remoteReceiverConnection.isConnected) {
            AppLog.w("【窗口裁剪】设备未连接，无法发送裁剪更新: ${remoteReceiverConnection.deviceName}")
            return
        }

        try {
            // 创建窗口变换控制消息
            val transformData = mapOf(
                "crop_left" to cropRatio.left,
                "crop_top" to cropRatio.top,
                "crop_right" to cropRatio.right,
                "crop_bottom" to cropRatio.bottom
            )

            val message = ControlMessage.createRemoteWindowTransformControl(
                connectionId = remoteReceiverConnection.id,
                targetWindowId = connectionId,
                transformType = "crop",
                transformData = transformData
            )

            // 通过RemoteReceiverManager发送消息
            val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            manager.sendWindowTransformControl(remoteReceiverConnection, message)

            AppLog.d("【窗口裁剪】裁剪更新消息已发送: $connectionId -> $cropRatio")

        } catch (e: Exception) {
            AppLog.e("【窗口裁剪】发送裁剪更新消息失败: $connectionId", e)
            Toast.makeText(requireContext(), "更新窗口裁剪失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    companion object {
        /**
         * 创建新的远程接收端控制对话框实例
         */
        fun newInstance(receiver: RemoteReceiverConnection): RemoteReceiverControlDialog {
            return RemoteReceiverControlDialog(receiver)
        }


    }
}
