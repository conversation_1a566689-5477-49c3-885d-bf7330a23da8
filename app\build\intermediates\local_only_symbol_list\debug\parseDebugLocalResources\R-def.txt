R_DEF: Internal format may change without notice
local
color black
color gray_dark
color gray_light
color gray_medium
color primary_blue
color primary_blue_dark
color purple_200
color purple_500
color purple_700
color selected_gray
color selected_gray_dark
color teal_200
color teal_700
color text_primary
color text_secondary
color white
drawable bottom_sheet_background
drawable button_apply_background
drawable button_background
drawable button_cancel_apply_background
drawable button_cancel_background
drawable button_delete_background
drawable button_primary_background
drawable button_reset_background
drawable circle_green
drawable color_circle_background
drawable connection_status_indicator
drawable count_badge_background
drawable crop_button_apply_background
drawable crop_button_cancel_background
drawable crop_button_reset_background
drawable crop_control_background
drawable dialog_background
drawable edit_text_background
drawable edittext_background
drawable floating_stopwatch_background
drawable ic_add
drawable ic_add_camera
drawable ic_add_media
drawable ic_add_picture
drawable ic_add_text
drawable ic_add_video
drawable ic_arrow_back
drawable ic_cast
drawable ic_check
drawable ic_clear
drawable ic_close
drawable ic_delete
drawable ic_director
drawable ic_drag_handle
drawable ic_edit
drawable ic_error
drawable ic_file
drawable ic_folder
drawable ic_font
drawable ic_format_align_center
drawable ic_format_align_left
drawable ic_format_align_right
drawable ic_format_bold
drawable ic_format_clear
drawable ic_format_italic
drawable ic_info
drawable ic_layer
drawable ic_notification
drawable ic_palette
drawable ic_refresh
drawable ic_remote_control
drawable ic_save
drawable ic_send
drawable ic_settings
drawable ic_stopwatch
drawable ic_updated
drawable ic_window_settings
drawable info_card_background
drawable item_applied_background
drawable item_normal_background
drawable item_selected_applied_background
drawable item_selected_background
drawable list_item_background
drawable palette
drawable precision_control_background
drawable rounded_background
drawable spinner_background
drawable wheel
id add_connection_button
id add_receiver_connection_button
id add_sender_connection_button
id apply_button
id audio_output_mode_group
id audio_video_server_switch
id batch_delete_button
id bitrate_seekbar
id bitrate_value_text
id border_color_text
id border_status_text
id border_width_text
id broadcast_button
id btn_add_font
id btn_add_font_size
id btn_add_front_camera
id btn_add_letter_spacing
id btn_add_line_spacing
id btn_add_media
id btn_add_picture
id btn_add_rear_camera
id btn_add_text
id btn_add_video
id btn_apply
id btn_apply_transform
id btn_bold
id btn_cancel
id btn_clear
id btn_clear_format
id btn_close
id btn_close_panel
id btn_color
id btn_confirm
id btn_delete
id btn_delete_color
id btn_director
id btn_drag_window
id btn_edit
id btn_italic
id btn_layer_manager
id btn_receive
id btn_remote_control
id btn_reset
id btn_reset_default
id btn_reset_transform
id btn_save
id btn_save_color
id btn_select_file
id btn_send
id btn_stopwatch
id btn_stroke
id btn_video_loop_count
id btn_window_color
id btn_window_manager
id button_group
id cancel_button
id cast_switch
id clear_screen_button
id close_button
id close_dialog_button
id colorPickerView
id confirm_button
id connect_button
id connection_count_text
id connection_id_text
id connection_status_indicator
id connection_status_text
id connection_text
id connections_container
id control_button
id control_buttons_container
id corner_radius_text
id crop_rect_text
id current_name_text
id delete_button
id detail_list
id device_address_text
id device_icon
id device_info_text
id device_name_input
id device_name_text
id dialog_title
id disconnect_button
id drag_handle
id earpiece_mode_radio
id edit_button
id empty_view
id et_b_input
id et_device_note
id et_font_name
id et_g_input
id et_hex_input
id et_new_font_size
id et_new_letter_spacing
id et_new_line_spacing
id et_position_x
id et_position_y
id et_r_input
id et_rotation_angle
id et_scale_factor
id et_stroke_width
id font_family_container
id font_size_container
id ip_address_input
id ip_address_text
id iv_alignment_icon
id iv_color_palette
id iv_current_indicator
id iv_delete_window
id iv_file_icon
id landscape_text
id layout_date_text
id layout_empty_state
id layout_list
id layout_name_input
id layout_name_text
id layout_video_controls
id layout_window_count_text
id letter_spacing_container
id line_spacing_container
id main_container
id media_audio_switch
id media_audio_volume_layout
id media_audio_volume_seekbar
id media_audio_volume_text
id mic_audio_switch
id mic_audio_volume_layout
id mic_audio_volume_seekbar
id mic_audio_volume_text
id mirror_text
id note_text
id opacity_text
id order_number
id port_info_text
id port_input
id radio_group_save_mode
id radio_replace_all
id radio_update_existing
id receive_button
id receiver_connection_count_text
id receiver_connections_recycler_view
id receiver_volume_seekbar
id receiver_volume_text
id remote_audio_output_mode_group
id remote_audio_video_switch
id remote_control_layout
id remote_control_status_text
id remote_control_switch
id remote_earpiece_mode_radio
id remote_speaker_mode_radio
id remote_volume_seekbar
id remote_volume_text
id resolution_info_text
id resolution_seekbar
id resolution_value_text
id rv_cast_windows
id rv_custom_palette
id rv_files
id rv_font_sizes
id rv_fonts
id rv_layer_devices
id rv_letter_spacing_list
id rv_line_spacing_list
id save_button
id seekbar_alpha
id seekbar_border_width
id seekbar_corner_radius
id seekbar_video_volume
id selection_button
id selection_checkbox
id sender_connection_count_text
id sender_connections_recycler_view
id server_status
id speaker_mode_radio
id spinner_font_family
id spinner_font_size
id spinner_letter_spacing
id spinner_line_spacing
id spinner_text_alignment
id start_screen_share_button
id stroke_control_container
id stroke_width_container
id surface_view_container
id switch_border
id switch_control
id switch_crop
id switch_drag
id switch_edit
id switch_landscape
id switch_mirror
id switch_rotation
id switch_scale
id switch_stroke_enabled
id switch_sync_control
id switch_video_play
id switch_visible
id switch_window_color_enabled
id tab_layout
id text_alignment_container
id title_bar
id tv_alignment_name
id tv_alpha_value
id tv_border_width_value
id tv_connection_id
id tv_corner_radius_value
id tv_current_font
id tv_current_font_size
id tv_current_letter_spacing
id tv_current_line_spacing
id tv_current_path
id tv_device_info
id tv_device_name
id tv_device_note
id tv_dialog_title
id tv_file_count
id tv_file_name
id tv_font_name
id tv_font_size
id tv_hex_value
id tv_ip_address
id tv_layer
id tv_letter_spacing
id tv_line_spacing
id tv_order_number
id tv_panel_title
id tv_position
id tv_preset_tag
id tv_rgb_value
id tv_rotation
id tv_scale
id tv_selected_file
id tv_time
id tv_transform_info
id tv_video_volume_value
id tv_window_count
id tv_window_settings_title
id tv_window_size
id update_button
id video_play_status_text
id view_color_item
id view_color_preview
id view_pager
id visibility_text
id visualization_container
id window_button
id window_color_control_container
id window_count_text
id window_visualization_view
layout activity_main
layout crop_control_buttons
layout dialog_add_media
layout dialog_add_receiver
layout dialog_add_remote_device
layout dialog_color_picker
layout dialog_director
layout dialog_edit_layout
layout dialog_font_file_picker
layout dialog_font_settings
layout dialog_font_size_settings
layout dialog_layer_manager
layout dialog_letter_spacing_settings
layout dialog_line_spacing_settings
layout dialog_note_edit
layout dialog_receive
layout dialog_remote_control_manager
layout dialog_remote_receiver_control
layout dialog_remote_receiver_settings_control
layout dialog_remote_sender_control
layout dialog_save_director_layout
layout dialog_save_options
layout dialog_send
layout dialog_window_settings
layout floating_stopwatch_window
layout fragment_receiver_tab
layout fragment_sender_tab
layout item_color_palette
layout item_connection
layout item_director_info
layout item_director_layout
layout item_font_file
layout item_font_setting
layout item_font_size_setting
layout item_layer
layout item_letter_spacing
layout item_line_spacing
layout item_remote_connection
layout item_remote_connection_control
layout item_remote_receiver
layout item_window_settings
layout layout_text_edit_panel
layout precision_control_panel
layout spinner_font_size_dropdown_item
layout spinner_font_size_item
layout spinner_letter_spacing_dropdown_item
layout spinner_letter_spacing_item
layout spinner_line_spacing_dropdown_item
layout spinner_line_spacing_item
layout spinner_text_alignment_dropdown_item
layout spinner_text_alignment_item
string add_receiver
string add_remote_connection
string alpha_percentage
string app_name
string bitrate_format
string cancel
string casting
string close
string confirm
string connect
string connected
string connection_count_format
string connection_id_format
string connection_id_parentheses_format
string connection_list
string control
string device_count_format
string device_name
string disconnect
string disconnected
string floating_stopwatch
string invalid_ip
string invalid_port
string ip_address
string layer_format
string media_audio_volume
string mic_audio_volume
string network_error
string order_number_format
string overlay_permission_required
string permission_denied
string port
string position_format
string receive_button
string receiver_settings
string remote_control
string remote_control_address_format
string remote_control_manager
string remote_control_server_start_failed
string remote_control_server_started
string remote_control_server_stopped
string remote_device_list
string resolution_info_format
string resolution_retry_format
string rotation_format
string scale_format
string send_button
string sender_settings
string server_running
string start_server
string stop_server
string stopwatch
string stopwatch_running
string stopwatch_start_failed
string stopwatch_started
string volume_format
string window_count_format
style DialogTheme
style SwitchTheme
style TabTextStyle
