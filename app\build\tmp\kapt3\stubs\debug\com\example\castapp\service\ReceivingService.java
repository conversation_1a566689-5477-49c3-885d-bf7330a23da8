package com.example.castapp.service;

/**
 * 接收服务
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\f\u0018\u0000 /2\u00020\u0001:\u0001/B\u0005\u00a2\u0006\u0002\u0010\u0002J*\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\r2\u0006\u0010\u0018\u001a\u00020\r2\u0006\u0010\u0019\u001a\u00020\u001a2\b\u0010\u001b\u001a\u0004\u0018\u00010\rH\u0002J\u0010\u0010\u001c\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\rH\u0002J\u0006\u0010\u001d\u001a\u00020\u0016J\b\u0010\u001e\u001a\u00020\u0016H\u0002J\b\u0010\u001f\u001a\u00020\u0016H\u0002J\b\u0010 \u001a\u00020\u0016H\u0002J\u0014\u0010!\u001a\u0004\u0018\u00010\"2\b\u0010#\u001a\u0004\u0018\u00010$H\u0016J\b\u0010%\u001a\u00020\u0016H\u0016J\b\u0010&\u001a\u00020\u0016H\u0016J\"\u0010\'\u001a\u00020\u001a2\b\u0010#\u001a\u0004\u0018\u00010$2\u0006\u0010(\u001a\u00020\u001a2\u0006\u0010)\u001a\u00020\u001aH\u0016J\u000e\u0010*\u001a\u00020\u00162\u0006\u0010+\u001a\u00020\rJ\u0010\u0010,\u001a\u00020\u00162\u0006\u0010-\u001a\u00020\u001aH\u0002J\b\u0010.\u001a\u00020\u0016H\u0002R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\t\u001a\u0004\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000RN\u0010\u000b\u001aB\u0012\f\u0012\n \u000e*\u0004\u0018\u00010\r0\r\u0012\f\u0012\n \u000e*\u0004\u0018\u00010\u00060\u0006 \u000e* \u0012\f\u0012\n \u000e*\u0004\u0018\u00010\r0\r\u0012\f\u0012\n \u000e*\u0004\u0018\u00010\u00060\u0006\u0018\u00010\f0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0011\u001a\u0004\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0013\u001a\u0004\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00060"}, d2 = {"Lcom/example/castapp/service/ReceivingService;", "Landroid/app/Service;", "()V", "audioReceivingManager", "Lcom/example/castapp/manager/AudioReceivingManager;", "isRunning", "", "messageReceivingManager", "Lcom/example/castapp/manager/MessageReceivingManager;", "outputSurface", "Landroid/view/Surface;", "processedDisconnections", "Ljava/util/concurrent/ConcurrentHashMap$KeySetView;", "", "kotlin.jvm.PlatformType", "stateManager", "Lcom/example/castapp/manager/StateManager;", "videoReceivingManager", "Lcom/example/castapp/manager/VideoReceivingManager;", "webSocketServer", "Lcom/example/castapp/websocket/WebSocketServer;", "handleConnectionRequest", "", "connectionId", "clientIP", "clientPort", "", "deviceName", "handleUIDisconnectNotification", "immediateNotifyAllConnectionsDisconnect", "initializeAudioReceivingManager", "initializeMessageReceivingManager", "initializeVideoReceivingManager", "onBind", "Landroid/os/IBinder;", "intent", "Landroid/content/Intent;", "onCreate", "onDestroy", "onStartCommand", "flags", "startId", "sendMessageToAllRemoteControllers", "messageJson", "startReceiving", "port", "stopReceiving", "Companion", "app_debug"})
public final class ReceivingService extends android.app.Service {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_START = "action_start";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_STOP = "action_stop";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_SET_AUDIO_OUTPUT_MODE = "action_set_audio_output_mode";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_SET_RECEIVER_VOLUME = "action_set_receiver_volume";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_PORT = "port";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_SPEAKER_MODE = "speaker_mode";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_VOLUME = "volume";
    private static final int WEBSOCKET_PORT_OFFSET = 1;
    private static final int MEDIA_AUDIO_PORT_OFFSET = 2;
    private static final int MIC_AUDIO_PORT_OFFSET = 3;
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile java.lang.ref.WeakReference<com.example.castapp.service.ReceivingService> serviceInstanceRef;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.websocket.WebSocketServer webSocketServer;
    @org.jetbrains.annotations.Nullable()
    private android.view.Surface outputSurface;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.manager.AudioReceivingManager audioReceivingManager;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.manager.VideoReceivingManager videoReceivingManager;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.manager.MessageReceivingManager messageReceivingManager;
    private boolean isRunning = false;
    private final java.util.concurrent.ConcurrentHashMap.KeySetView<java.lang.String, java.lang.Boolean> processedDisconnections = null;
    private com.example.castapp.manager.StateManager stateManager;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.service.ReceivingService.Companion Companion = null;
    
    public ReceivingService() {
        super();
    }
    
    @java.lang.Override()
    public void onCreate() {
    }
    
    /**
     * 初始化音频接收管理器
     */
    private final void initializeAudioReceivingManager() {
    }
    
    /**
     * 初始化视频接收管理器
     */
    private final void initializeVideoReceivingManager() {
    }
    
    /**
     * 初始化消息接收管理器
     */
    private final void initializeMessageReceivingManager() {
    }
    
    @java.lang.Override()
    public int onStartCommand(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent, int flags, int startId) {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.os.IBinder onBind(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent) {
        return null;
    }
    
    /**
     * 开始接收
     */
    private final void startReceiving(int port) {
    }
    
    /**
     * 停止接收
     */
    private final void stopReceiving() {
    }
    
    /**
     * 立即通知所有连接断开（供外部调用，在用户点击停止时立即执行）
     */
    public final void immediateNotifyAllConnectionsDisconnect() {
    }
    
    /**
     * 处理UI断开通知
     */
    private final void handleUIDisconnectNotification(java.lang.String connectionId) {
    }
    
    /**
     * 处理连接请求，自动添加发送端连接信息到StateManager
     */
    private final void handleConnectionRequest(java.lang.String connectionId, java.lang.String clientIP, int clientPort, java.lang.String deviceName) {
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    /**
     * 🔄 发送消息到所有连接的遥控端
     */
    public final void sendMessageToAllRemoteControllers(@org.jetbrains.annotations.NotNull()
    java.lang.String messageJson) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0004J\b\u0010\u0015\u001a\u00020\u0013H\u0002J\u000e\u0010\u0016\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0004J\b\u0010\u0017\u001a\u0004\u0018\u00010\u0011J\b\u0010\u0018\u001a\u0004\u0018\u00010\u0019J\u000e\u0010\u001a\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0004J\u0006\u0010\u001b\u001a\u00020\u0013J\u0006\u0010\u001c\u001a\u00020\u0013J\u0010\u0010\u001d\u001a\u00020\u00132\u0006\u0010\u001e\u001a\u00020\u0011H\u0002J\u0018\u0010\u001f\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u00042\b\u0010 \u001a\u0004\u0018\u00010!R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\fX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\fX\u0082T\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000f\u001a\n\u0012\u0004\u0012\u00020\u0011\u0018\u00010\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\""}, d2 = {"Lcom/example/castapp/service/ReceivingService$Companion;", "", "()V", "ACTION_SET_AUDIO_OUTPUT_MODE", "", "ACTION_SET_RECEIVER_VOLUME", "ACTION_START", "ACTION_STOP", "EXTRA_PORT", "EXTRA_SPEAKER_MODE", "EXTRA_VOLUME", "MEDIA_AUDIO_PORT_OFFSET", "", "MIC_AUDIO_PORT_OFFSET", "WEBSOCKET_PORT_OFFSET", "serviceInstanceRef", "Ljava/lang/ref/WeakReference;", "Lcom/example/castapp/service/ReceivingService;", "clearDisconnectionFlag", "", "connectionId", "clearInstance", "disconnectSpecificConnection", "getInstance", "getWebSocketServer", "Lcom/example/castapp/websocket/WebSocketServer;", "gracefulStopDecoderForConnection", "immediateNotifyDisconnectFromExternal", "immediateStopAllDecodersFromExternal", "setInstance", "service", "setSurfaceForConnection", "surface", "Landroid/view/Surface;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        private final void setInstance(com.example.castapp.service.ReceivingService service) {
        }
        
        @org.jetbrains.annotations.Nullable()
        public final com.example.castapp.service.ReceivingService getInstance() {
            return null;
        }
        
        private final void clearInstance() {
        }
        
        public final void setSurfaceForConnection(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.Nullable()
        android.view.Surface surface) {
        }
        
        public final void clearDisconnectionFlag(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId) {
        }
        
        /**
         * 从外部立即通知所有连接断开（供ViewModel调用）
         */
        public final void immediateNotifyDisconnectFromExternal() {
        }
        
        /**
         * 从外部立即停止所有MediaCodec解码器（供ViewModel调用，避免Surface清理冲突）
         */
        public final void immediateStopAllDecodersFromExternal() {
        }
        
        /**
         * 🗑️ 断开特定WebSocket连接（供窗口删除功能调用）
         */
        public final void disconnectSpecificConnection(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId) {
        }
        
        /**
         * 🚀 优雅停止特定连接的MediaCodec解码器（供窗口删除功能调用）
         */
        public final void gracefulStopDecoderForConnection(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId) {
        }
        
        /**
         * 🎯 获取WebSocket服务器实例（供外部调用）
         */
        @org.jetbrains.annotations.Nullable()
        public final com.example.castapp.websocket.WebSocketServer getWebSocketServer() {
            return null;
        }
    }
}