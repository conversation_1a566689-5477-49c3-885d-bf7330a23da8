<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="#F5F5F5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:gravity="center_vertical">

        <!-- 窗口信息区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 设备名称和连接ID - 同一行显示 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <!-- 排序序号 -->
                <TextView
                    android:id="@+id/tv_order_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="1. "
                    android:textStyle="bold"
                    android:textSize="12sp"
                    android:textColor="#FF9800"
                    android:layout_marginEnd="2dp" />

                <TextView
                    android:id="@+id/tv_device_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="vivo V2244A"
                    android:textStyle="bold"
                    android:textSize="12sp"
                    android:textColor="#2196F3"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:maxWidth="140dp" />

                <TextView
                    android:id="@+id/tv_connection_id"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="（ID:12345678）"
                    android:textStyle="bold"
                    android:textSize="12sp"
                    android:textColor="#2196F3" />

                <!-- 删除图标 -->
                <ImageView
                    android:id="@+id/iv_delete_window"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_delete"
                    android:background="?android:attr/selectableItemBackgroundBorderless"
                    android:padding="2dp"
                    android:layout_marginStart="8dp"
                    android:contentDescription="删除窗口"
                    app:tint="#B2B2B2" />

            </LinearLayout>

            <!-- IP地址和端口 - 辅助信息同一行 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_ip_address"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="*************:8080"
                    android:textSize="10sp"
                    android:textColor="#999999" />

                <TextView
                    android:id="@+id/tv_window_size"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="窗口尺寸:108×240"
                    android:textSize="10sp"
                    android:textColor="#999999" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="2dp"
                android:gravity="center_vertical">

                <!-- 变换信息 - 位置、缩放、旋转 -->
                <TextView
                    android:id="@+id/tv_transform_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="位置（50,100） 缩放：1.0  旋转：0°"
                    android:textSize="10sp"
                    android:textColor="#666666"
                    android:layout_marginEnd="8dp"/>

            </LinearLayout>

            <!-- 功能控制开关区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <!-- 拖动开关 -->
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_drag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="拖动"
                    android:textSize="12sp"
                    android:textColor="#666666"
                    android:checked="false"
                    android:scaleX="0.8"
                    android:scaleY="0.8"
                    android:layout_marginStart="-8dp"
                    android:contentDescription="开启/关闭拖动功能" />

                <!-- 缩放开关 -->
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_scale"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="缩放"
                    android:textSize="12sp"
                    android:textColor="#666666"
                    android:checked="false"
                    android:scaleX="0.8"
                    android:scaleY="0.8"
                    android:layout_marginStart="-10dp"
                    android:contentDescription="开启/关闭缩放功能" />

                <!-- 旋转开关 -->
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_rotation"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="旋转"
                    android:textSize="12sp"
                    android:textColor="#666666"
                    android:checked="false"
                    android:scaleX="0.8"
                    android:scaleY="0.8"
                    android:layout_marginStart="-10dp"
                    android:contentDescription="开启/关闭旋转功能" />

                <!-- 裁剪开关 -->
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_crop"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="裁剪"
                    android:textSize="12sp"
                    android:textColor="#666666"
                    android:checked="false"
                    android:scaleX="0.8"
                    android:scaleY="0.8"
                    android:layout_marginStart="-10dp"
                    android:contentDescription="开启/关闭裁剪模式" />

                <!-- 隐藏开关 -->
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_visible"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="显示"
                    android:textSize="12sp"
                    android:textColor="#666666"
                    android:checked="true"
                    android:scaleX="0.8"
                    android:scaleY="0.8"
                    android:layout_marginStart="-8dp"
                    android:contentDescription="显示/隐藏投屏窗口" />

            </LinearLayout>

            <!-- 功能控制开关区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <!-- 镜像开关 -->
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_mirror"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="镜像"
                    android:textSize="12sp"
                    android:textColor="#666666"
                    android:checked="false"
                    android:scaleX="0.8"
                    android:scaleY="0.8"
                    android:layout_marginStart="-8dp"
                    android:contentDescription="开启/关闭镜像画面" />

                <!-- 横屏开关 -->
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_landscape"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="横屏"
                    android:textSize="12sp"
                    android:textColor="#666666"
                    android:checked="false"
                    android:scaleX="0.8"
                    android:scaleY="0.8"
                    android:layout_marginStart="-10dp"
                    android:contentDescription="开启/关闭横屏模式检测" />

                <!-- 调控开关 -->
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_control"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="调控"
                    android:textSize="12sp"
                    android:textColor="#666666"
                    android:checked="false"
                    android:scaleX="0.8"
                    android:scaleY="0.8"
                    android:layout_marginStart="-10dp"
                    android:contentDescription="开启/关闭精准调控面板" />

                <!-- 编辑开关（仅文字窗口显示） -->
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_edit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="编辑"
                    android:textSize="12sp"
                    android:textColor="#666666"
                    android:checked="false"
                    android:scaleX="0.8"
                    android:scaleY="0.8"
                    android:layout_marginStart="-10dp"
                    android:contentDescription="开启/关闭文字编辑模式"
                    android:visibility="gone" />

                <!-- 边框开关 -->
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_border"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="边框"
                    android:textSize="12sp"
                    android:textColor="#666666"
                    android:checked="false"
                    android:scaleX="0.8"
                    android:scaleY="0.8"
                    android:layout_marginStart="-10dp"
                    android:contentDescription="开启/关闭投屏窗口边框" />

                <!-- 色板图标 -->
                <ImageView
                    android:id="@+id/iv_color_palette"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginStart="-6dp"
                    android:src="@drawable/ic_palette"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:padding="6dp"
                    android:enabled="false"
                    android:alpha="0.3"
                    android:clickable="true"
                    android:focusable="true"
                    android:contentDescription="选择边框颜色" />


            </LinearLayout>

            <!-- 视频播放控制区域（仅视频窗口显示） -->
            <LinearLayout
                android:id="@+id/layout_video_controls"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <!-- 播放开关和播放次数 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/switch_video_play"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="播放"
                        android:textSize="12sp"
                        android:textColor="#666666"
                        android:checked="true"
                        android:layout_marginStart="-8dp"
                        android:scaleX="0.8"
                        android:scaleY="0.8"/>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="次数:"
                        android:textSize="10sp"
                        android:textColor="#666666"
                        android:layout_gravity="center_vertical"/>

                    <Button
                        android:id="@+id/btn_video_loop_count"
                        android:layout_width="25dp"
                        android:layout_height="18dp"
                        android:text="∞"
                        android:textSize="10sp"
                        android:textColor="#4CAF50"
                        android:background="#E8F5E8"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="6dp"/>

                    <View
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_weight="1"/>

                </LinearLayout>

                <!-- 音量控制 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginTop="2dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="18dp"
                        android:text="播放音量:"
                        android:textSize="10sp"
                        android:textColor="#666666"/>

                    <SeekBar
                        android:id="@+id/seekbar_video_volume"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="100"
                        android:progress="80"
                        android:layout_marginStart="-6dp"/>

                    <TextView
                        android:id="@+id/tv_video_volume_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="80%"
                        android:textSize="10sp"
                        android:textColor="#666666"
                        android:minWidth="40dp"
                        android:gravity="start"/>

                </LinearLayout>

            </LinearLayout>

            <!-- 边框控制区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="4dp">

                <!-- 边框宽度滑动条 -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="18dp"
                    android:text="边框宽度:"
                    android:textSize="10sp"
                    android:textColor="#666666"/>

                <SeekBar
                    android:id="@+id/seekbar_border_width"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="19"
                    android:progress="1"
                    android:enabled="false"
                    android:layout_marginStart="-6dp"
                    android:alpha="0.3" />

                <TextView
                    android:id="@+id/tv_border_width_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2dp"
                    android:textSize="10sp"
                    android:textColor="#666666"
                    android:minWidth="40dp"
                    android:gravity="start" />

            </LinearLayout>

            <!-- 圆角滑动条区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="2dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="18dp"
                    android:text="圆角半径:"
                    android:textColor="#666666"
                    android:textSize="10sp" />

                <SeekBar
                    android:id="@+id/seekbar_corner_radius"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="150"
                    android:progress="16"
                    android:layout_marginStart="-6dp"/>

                <TextView
                    android:id="@+id/tv_corner_radius_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="16dp"
                    android:textSize="10sp"
                    android:textColor="#666666"
                    android:minWidth="40dp"
                    android:gravity="start" />

            </LinearLayout>

            <!-- 透明度滑动条区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="2dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=" 透 明 度 :"
                    android:textSize="10sp"
                    android:textColor="#666666"/>

                <SeekBar
                    android:id="@+id/seekbar_alpha"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="100"
                    android:progress="100"
                    android:layout_marginStart="-6dp"/>

                <TextView
                    android:id="@+id/tv_alpha_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="100%"
                    android:textSize="10sp"
                    android:textColor="#666666"
                    android:minWidth="40dp"
                    android:gravity="start" />

            </LinearLayout>

            <!-- 🏷️ 设备备注行 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="2dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="18dp"
                    android:text="   备   注 :"
                    android:textSize="10sp"
                    android:textColor="#666666" />

                <TextView
                    android:id="@+id/tv_device_note"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="无"
                    android:textSize="10sp"
                    android:layout_marginStart="4dp"
                    android:textColor="#666666"
                    android:maxLines="5"
                    android:ellipsize="end"
                    android:lineSpacingExtra="2dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:layout_marginEnd="12dp"
                    android:contentDescription="点击编辑设备备注" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>


</androidx.cardview.widget.CardView>
