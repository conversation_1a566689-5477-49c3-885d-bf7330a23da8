#Sat Aug 23 10:12:33 CST 2025
com.example.castapp-main-5\:/layout/item_line_spacing.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_line_spacing.xml
com.example.castapp-main-5\:/layout/dialog_remote_control_manager.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_remote_control_manager.xml
com.example.castapp-main-5\:/layout/item_connection.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_connection.xml
com.example.castapp-main-5\:/drawable/button_reset_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_reset_background.xml
com.example.castapp-main-5\:/drawable/ic_check.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_check.xml
com.example.castapp-main-5\:/layout/item_font_size_setting.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_font_size_setting.xml
com.example.castapp-main-5\:/layout/spinner_line_spacing_item.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\spinner_line_spacing_item.xml
com.example.castapp-main-5\:/drawable/info_card_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\info_card_background.xml
com.example.castapp-main-5\:/drawable/ic_send.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_send.xml
com.example.castapp-main-5\:/drawable/ic_font.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_font.xml
com.example.castapp-main-5\:/drawable/floating_stopwatch_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\floating_stopwatch_background.xml
com.example.castapp-main-5\:/layout/spinner_text_alignment_item.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\spinner_text_alignment_item.xml
com.example.castapp-main-5\:/layout/item_color_palette.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_color_palette.xml
com.example.castapp-main-5\:/layout/dialog_font_settings.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_font_settings.xml
com.example.castapp-main-5\:/layout/dialog_note_edit.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_note_edit.xml
com.example.castapp-main-5\:/layout/dialog_add_remote_device.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_add_remote_device.xml
com.example.castapp-main-5\:/layout/dialog_window_settings.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_window_settings.xml
com.example.castapp-main-5\:/drawable/button_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_background.xml
com.example.castapp-main-5\:/layout/dialog_edit_layout.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_edit_layout.xml
com.example.castapp-main-5\:/drawable/connection_status_indicator.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\connection_status_indicator.xml
com.example.castapp-main-5\:/drawable/circle_green.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_green.xml
com.example.castapp-main-5\:/layout/dialog_font_file_picker.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_font_file_picker.xml
com.example.castapp-main-5\:/drawable/dialog_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\dialog_background.xml
com.example.castapp-main-5\:/drawable/crop_button_apply_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\crop_button_apply_background.xml
com.example.castapp-main-5\:/layout/spinner_letter_spacing_item.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\spinner_letter_spacing_item.xml
com.example.castapp-main-5\:/layout/dialog_letter_spacing_settings.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_letter_spacing_settings.xml
com.example.castapp-main-5\:/layout/fragment_sender_tab.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_sender_tab.xml
com.example.castapp-main-5\:/drawable/precision_control_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\precision_control_background.xml
com.example.castapp-main-5\:/drawable/ic_add.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_add.xml
com.example.castapp-main-5\:/layout/dialog_layer_manager.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_layer_manager.xml
com.example.castapp-main-5\:/drawable/ic_refresh.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_refresh.xml
com.example.castapp-main-5\:/drawable/item_normal_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\item_normal_background.xml
com.example.castapp-main-5\:/drawable/ic_settings.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_settings.xml
com.example.castapp-main-5\:/layout/spinner_letter_spacing_dropdown_item.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\spinner_letter_spacing_dropdown_item.xml
com.example.castapp-main-5\:/drawable/item_selected_applied_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\item_selected_applied_background.xml
com.example.castapp-main-5\:/drawable/ic_format_align_left.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_format_align_left.xml
com.example.castapp-main-5\:/layout/dialog_remote_receiver_control.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_remote_receiver_control.xml
com.example.castapp-main-5\:/drawable/item_selected_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\item_selected_background.xml
com.example.castapp-main-5\:/layout/item_font_setting.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_font_setting.xml
com.example.castapp-main-5\:/drawable/ic_arrow_back.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_arrow_back.xml
com.example.castapp-main-5\:/layout/dialog_receive.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_receive.xml
com.example.castapp-main-5\:/drawable/button_apply_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_apply_background.xml
com.example.castapp-main-5\:/drawable/ic_format_align_center.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_format_align_center.xml
com.example.castapp-main-5\:/layout/item_director_layout.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_director_layout.xml
com.example.castapp-main-5\:/drawable/crop_button_reset_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\crop_button_reset_background.xml
com.example.castapp-main-5\:/drawable/rounded_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_background.xml
com.example.castapp-main-5\:/drawable/button_cancel_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_cancel_background.xml
com.example.castapp-main-5\:/layout/item_letter_spacing.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_letter_spacing.xml
com.example.castapp-main-5\:/drawable/ic_delete.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_delete.xml
com.example.castapp-main-5\:/drawable/button_primary_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_primary_background.xml
com.example.castapp-main-5\:/drawable/ic_info.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_info.xml
com.example.castapp-main-5\:/drawable/color_circle_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\color_circle_background.xml
com.example.castapp-main-5\:/drawable/ic_add_media.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_add_media.xml
com.example.castapp-main-5\:/drawable/wheel.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\wheel.xml
com.example.castapp-main-5\:/drawable/ic_add_video.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_add_video.xml
com.example.castapp-main-5\:/layout/dialog_remote_receiver_settings_control.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_remote_receiver_settings_control.xml
com.example.castapp-main-5\:/drawable/crop_control_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\crop_control_background.xml
com.example.castapp-main-5\:/layout/item_remote_connection.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_remote_connection.xml
com.example.castapp-main-5\:/drawable/ic_cast.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_cast.xml
com.example.castapp-main-5\:/drawable/ic_drag_handle.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_drag_handle.xml
com.example.castapp-main-5\:/layout/spinner_font_size_dropdown_item.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\spinner_font_size_dropdown_item.xml
com.example.castapp-main-5\:/drawable/ic_close.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_close.xml
com.example.castapp-main-5\:/layout/dialog_send.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_send.xml
com.example.castapp-main-5\:/layout/item_font_file.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_font_file.xml
com.example.castapp-main-5\:/drawable/bottom_sheet_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bottom_sheet_background.xml
com.example.castapp-main-5\:/drawable/list_item_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\list_item_background.xml
com.example.castapp-main-5\:/layout/item_remote_connection_control.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_remote_connection_control.xml
com.example.castapp-main-5\:/drawable/ic_notification.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_notification.xml
com.example.castapp-main-5\:/drawable/ic_add_text.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_add_text.xml
com.example.castapp-main-5\:/drawable/item_applied_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\item_applied_background.xml
com.example.castapp-main-5\:/layout/spinner_text_alignment_dropdown_item.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\spinner_text_alignment_dropdown_item.xml
com.example.castapp-main-5\:/layout/dialog_line_spacing_settings.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_line_spacing_settings.xml
com.example.castapp-main-5\:/drawable/ic_window_settings.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_window_settings.xml
com.example.castapp-main-5\:/layout/dialog_save_director_layout.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_save_director_layout.xml
com.example.castapp-main-5\:/layout/dialog_save_options.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_save_options.xml
com.example.castapp-main-5\:/layout/floating_stopwatch_window.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\floating_stopwatch_window.xml
com.example.castapp-main-5\:/drawable/ic_save.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_save.xml
com.example.castapp-main-5\:/drawable/ic_palette.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_palette.xml
com.example.castapp-main-5\:/drawable/ic_edit.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_edit.xml
com.example.castapp-main-5\:/drawable/palette.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\palette.xml
com.example.castapp-main-5\:/layout/fragment_receiver_tab.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_receiver_tab.xml
com.example.castapp-main-5\:/drawable/ic_clear.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_clear.xml
com.example.castapp-main-5\:/layout/layout_text_edit_panel.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\layout_text_edit_panel.xml
com.example.castapp-main-5\:/drawable/ic_add_camera.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_add_camera.xml
com.example.castapp-main-5\:/drawable/ic_folder.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_folder.xml
com.example.castapp-main-5\:/drawable/ic_format_clear.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_format_clear.xml
com.example.castapp-main-5\:/layout/dialog_add_media.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_add_media.xml
com.example.castapp-main-5\:/drawable/spinner_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\spinner_background.xml
com.example.castapp-main-5\:/layout/dialog_color_picker.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_color_picker.xml
com.example.castapp-main-5\:/layout/dialog_font_size_settings.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_font_size_settings.xml
com.example.castapp-main-5\:/layout/activity_main.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_main.xml
com.example.castapp-main-5\:/layout/crop_control_buttons.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\crop_control_buttons.xml
com.example.castapp-main-5\:/drawable/button_cancel_apply_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_cancel_apply_background.xml
com.example.castapp-main-5\:/drawable/count_badge_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\count_badge_background.xml
com.example.castapp-main-5\:/layout/precision_control_panel.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\precision_control_panel.xml
com.example.castapp-main-5\:/drawable/ic_add_picture.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_add_picture.xml
com.example.castapp-main-5\:/layout/dialog_add_receiver.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_add_receiver.xml
com.example.castapp-main-5\:/drawable/ic_director.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_director.xml
com.example.castapp-main-5\:/drawable/ic_format_italic.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_format_italic.xml
com.example.castapp-main-5\:/drawable/ic_file.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_file.xml
com.example.castapp-main-5\:/drawable/ic_format_bold.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_format_bold.xml
com.example.castapp-main-5\:/drawable/ic_format_align_right.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_format_align_right.xml
com.example.castapp-main-5\:/drawable/ic_layer.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_layer.xml
com.example.castapp-main-5\:/drawable/ic_stopwatch.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_stopwatch.xml
com.example.castapp-main-5\:/layout/item_director_info.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_director_info.xml
com.example.castapp-main-5\:/drawable/ic_error.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_error.xml
com.example.castapp-main-5\:/layout/item_layer.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_layer.xml
com.example.castapp-main-5\:/layout/dialog_director.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_director.xml
com.example.castapp-main-5\:/drawable/button_delete_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_delete_background.xml
com.example.castapp-main-5\:/drawable/edittext_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\edittext_background.xml
com.example.castapp-main-5\:/layout/item_window_settings.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_window_settings.xml
com.example.castapp-main-5\:/drawable/ic_updated.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_updated.xml
com.example.castapp-main-5\:/drawable/crop_button_cancel_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\crop_button_cancel_background.xml
com.example.castapp-main-5\:/layout/spinner_font_size_item.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\spinner_font_size_item.xml
com.example.castapp-main-5\:/drawable/ic_remote_control.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_remote_control.xml
com.example.castapp-main-5\:/drawable/edit_text_background.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\edit_text_background.xml
com.example.castapp-main-5\:/layout/spinner_line_spacing_dropdown_item.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\spinner_line_spacing_dropdown_item.xml
com.example.castapp-main-5\:/layout/item_remote_receiver.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_remote_receiver.xml
com.example.castapp-main-5\:/layout/dialog_remote_sender_control.xml=D\:\\Android\\AndroidProject\\CastAPP\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_remote_sender_control.xml
