package com.example.castapp.ui.dialog

import android.content.Context
import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.widget.SwitchCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.example.castapp.R
import com.example.castapp.model.CastWindowInfo
import com.example.castapp.ui.adapter.WindowManagerAdapter
import com.example.castapp.utils.AppLog
import com.example.castapp.websocket.ControlMessage
import com.example.castapp.model.WindowUpdateMode
import com.example.castapp.model.RemoteReceiverConnection

/**
 * 🪟 远程投屏窗口管理BottomSheet对话框
 * 显示远程接收端的所有活跃投屏窗口信息（只读模式）
 */
class RemoteWindowManagerDialog(
    private val remoteReceiverConnection: RemoteReceiverConnection
) : BottomSheetDialogFragment() {

    // UI组件
    private lateinit var tvWindowSettingsTitle: TextView // 🎯 窗口设置标题
    private lateinit var tvWindowCount: TextView
    private lateinit var rvCastWindows: RecyclerView
    private lateinit var layoutEmptyState: LinearLayout
    private lateinit var switchSyncControl: SwitchCompat // 🔄 同步开关
    private lateinit var btnClose: ImageView // 🔄 关闭按钮
    private lateinit var adapter: WindowManagerAdapter

    // 窗口信息列表
    private var windowInfoList: List<CastWindowInfo> = emptyList()

    // 🔄 同步控制状态
    private var isSyncEnabled = false

    // 🎯 当前更新模式
    private var currentUpdateMode: WindowUpdateMode = WindowUpdateMode.PARTIAL

    // 对话框关闭回调
    var onDialogDismissed: (() -> Unit)? = null

    // 🪟 窗口信息更新回调（用于同步可视化数据）
    var onWindowInfoUpdated: ((List<CastWindowInfo>, WindowUpdateMode) -> Unit)? = null

    // 🎯 裁剪模式控制回调
    var onCropModeControl: ((String, Boolean) -> Unit)? = null

    companion object {
        fun newInstance(remoteReceiverConnection: RemoteReceiverConnection): RemoteWindowManagerDialog {
            return RemoteWindowManagerDialog(remoteReceiverConnection)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_window_settings, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews(view)
        setupRecyclerView()
        setupClickListeners()
        updateTitle()

        // 注册对话框到管理器
        registerDialog()

        // 🎯 请求窗口信息（部分更新模式）
        requestWindowInfo(WindowUpdateMode.PARTIAL)

        AppLog.d("【远程窗口管理】对话框已创建: ${remoteReceiverConnection.deviceName}")
    }

    /**
     * 初始化视图组件
     */
    private fun initViews(view: View) {
        tvWindowSettingsTitle = view.findViewById(R.id.tv_window_settings_title)
        tvWindowCount = view.findViewById(R.id.tv_window_count)
        rvCastWindows = view.findViewById(R.id.rv_cast_windows)
        layoutEmptyState = view.findViewById(R.id.layout_empty_state)
        switchSyncControl = view.findViewById(R.id.switch_sync_control)
        btnClose = view.findViewById(R.id.btn_close)

        // 🔄 先恢复保存的同步开关状态，再设置监听器
        restoreSyncControlState()
        setupSyncControlSwitch()

        // � 统一编辑状态管理：不再需要恢复遥控端编辑状态，统一使用WindowSettingsManager
    }

    /**
     * 🔄 恢复保存的同步开关状态
     */
    private fun restoreSyncControlState() {
        val sharedPrefs = requireContext().getSharedPreferences("remote_window_settings", Context.MODE_PRIVATE)
        val syncStateKey = "sync_enabled_${remoteReceiverConnection.id}"
        isSyncEnabled = sharedPrefs.getBoolean(syncStateKey, false)

        // 设置UI状态（不触发监听器）
        switchSyncControl.setOnCheckedChangeListener(null)
        switchSyncControl.isChecked = isSyncEnabled

        AppLog.d("【远程窗口同步】恢复同步开关状态: $isSyncEnabled (设备: ${remoteReceiverConnection.deviceName})")
    }

    /**
     * 🔄 保存同步开关状态
     */
    private fun saveSyncControlState() {
        val sharedPrefs = requireContext().getSharedPreferences("remote_window_settings", Context.MODE_PRIVATE)
        val syncStateKey = "sync_enabled_${remoteReceiverConnection.id}"
        sharedPrefs.edit().putBoolean(syncStateKey, isSyncEnabled).apply()

        AppLog.d("【远程窗口同步】保存同步开关状态: $isSyncEnabled (设备: ${remoteReceiverConnection.deviceName})")
    }

    /**
     * 🔄 设置同步开关监听器
     */
    private fun setupSyncControlSwitch() {
        switchSyncControl.setOnCheckedChangeListener { _, isChecked ->
            isSyncEnabled = isChecked
            updateAdapterSyncMode()

            // 🔧 保存状态到SharedPreferences
            saveSyncControlState()

            AppLog.d("【远程窗口同步】同步开关状态变更: $isChecked")

            if (isChecked) {
                Toast.makeText(requireContext(), "已启用窗口同步控制", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(requireContext(), "已禁用窗口同步控制", Toast.LENGTH_SHORT).show()
            }
        }
    }
    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        btnClose.setOnClickListener {
            AppLog.d("【远程窗口管理】用户点击关闭按钮")
            dismiss()
        }
    }

    /**
     * 🔄 更新适配器同步模式
     */
    private fun updateAdapterSyncMode() {
        if (::adapter.isInitialized) {
            // 🔧 修复：控件始终保持启用状态，不受同步开关影响
            // 远程窗口管理场景下，控件应该始终可用，同步开关只控制是否发送WebSocket消息
            adapter.setRemoteMode(false)

            // 🎯 关键修复：编辑开关监听器始终设置，不受同步开关影响
            setupEditSwitchListener()

            // 如果同步开关开启，设置其他窗口操作监听器
            if (isSyncEnabled) {
                setupWindowOperationListeners()
            } else {
                clearWindowOperationListeners()
            }

            // 🔧 修复：只更新监听器设置，不重置UI状态
            adapter.updateListenersOnly()
        }
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        adapter = WindowManagerAdapter()
        rvCastWindows.layoutManager = LinearLayoutManager(context)
        rvCastWindows.adapter = adapter

        // 🔧 修复：初始状态控件始终启用，同步开关只控制消息发送
        updateAdapterSyncMode()

        // 🎯 设置裁剪开关监听器
        setupCropSwitchListener()

        AppLog.d("【远程窗口管理】RecyclerView设置完成")
    }

    /**
     * 🎯 设置裁剪开关监听器
     */
    private fun setupCropSwitchListener() {
        adapter.setOnCropSwitchListener(object : WindowManagerAdapter.OnCropSwitchListener {
            override fun onCropSwitchChanged(connectionId: String, isEnabled: Boolean) {
                AppLog.d("【远程窗口管理】裁剪开关变化: $connectionId, 启用: $isEnabled")

                // 通知远程控制窗口进行裁剪模式控制
                onCropModeControl?.invoke(connectionId, isEnabled)
            }
        })
    }

    /**
     * 更新标题显示
     */
    private fun updateTitle() {
        // 由于使用的是现有布局，标题已经在布局文件中设置
        // 这里可以通过其他方式显示远程设备信息，比如在窗口数量旁边显示
        AppLog.d("【远程窗口管理】标题更新完成，远程设备: ${remoteReceiverConnection.deviceName}")
    }

    /**
     * 🪟 请求远程窗口信息
     * @param updateMode 更新模式，决定是否同步更新可视化窗口
     */
    private fun requestWindowInfo(updateMode: WindowUpdateMode = WindowUpdateMode.PARTIAL) {
        try {
            AppLog.d("【远程窗口管理】发送窗口信息请求到: ${remoteReceiverConnection.deviceName}, 更新模式: $updateMode")

            // 保存当前更新模式，用于响应处理时使用
            currentUpdateMode = updateMode

            // 通过RemoteReceiverManager发送窗口管理请求
            val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            manager.sendWindowManagerRequest(remoteReceiverConnection)

            // 显示加载状态
            showLoadingState()

        } catch (e: Exception) {
            AppLog.e("【远程窗口管理】发送窗口信息请求失败", e)
            showErrorState("请求失败: ${e.message}")
        }
    }

    /**
     * 🪟 处理接收到的窗口信息响应
     */
    fun handleWindowManagerResponse(message: ControlMessage) {
        try {
            val success = message.getBooleanData("success") ?: false

            if (success) {
                val windowCount = message.getIntData("window_count") ?: 0
                val windowInfoListData = message.data["window_info_list"] as? List<*>

                AppLog.d("【远程窗口管理】收到窗口信息响应，窗口数量: $windowCount")

                if (windowInfoListData != null) {
                    // 解析窗口信息列表
                    val parsedWindowList = parseWindowInfoList(windowInfoListData)
                    updateWindowList(parsedWindowList)
                } else {
                    showErrorState("窗口信息解析失败")
                }
            } else {
                val errorMessage = message.getStringData("error") ?: "未知错误"
                showErrorState("获取窗口信息失败: $errorMessage")
            }

        } catch (e: Exception) {
            AppLog.e("【远程窗口管理】处理窗口信息响应失败", e)
            showErrorState("数据处理失败: ${e.message}")
        }
    }

    /**
     * 解析窗口信息列表
     */
    private fun parseWindowInfoList(windowInfoListData: List<*>): List<CastWindowInfo> {
        return windowInfoListData.mapNotNull { item ->
            try {
                if (item is Map<*, *>) {
                    val windowData = item as Map<String, Any>

                    // 🪟 解析裁剪区域信息
                    val cropRectRatio = (windowData["cropRectRatio"] as? Map<*, *>)?.let { cropData ->
                        android.graphics.RectF(
                            (cropData["left"] as? Number)?.toFloat() ?: 0f,
                            (cropData["top"] as? Number)?.toFloat() ?: 0f,
                            (cropData["right"] as? Number)?.toFloat() ?: 1f,
                            (cropData["bottom"] as? Number)?.toFloat() ?: 1f
                        )
                    }

                    // 🪟 使用实际显示尺寸（考虑裁剪）
                    val actualDisplayWidth = (windowData["actualDisplayWidth"] as? Number)?.toInt() ?:
                                            (windowData["baseWindowWidth"] as? Number)?.toInt() ?: 0
                    val actualDisplayHeight = (windowData["actualDisplayHeight"] as? Number)?.toInt() ?:
                                             (windowData["baseWindowHeight"] as? Number)?.toInt() ?: 0

                    CastWindowInfo(
                        connectionId = windowData["connectionId"] as? String ?: "",
                        ipAddress = windowData["ipAddress"] as? String ?: "",
                        port = (windowData["port"] as? Number)?.toInt() ?: 0,
                        isActive = windowData["isActive"] as? Boolean ?: true,
                        deviceName = windowData["deviceName"] as? String,
                        positionX = (windowData["positionX"] as? Number)?.toFloat() ?: 0f,
                        positionY = (windowData["positionY"] as? Number)?.toFloat() ?: 0f,
                        scaleFactor = (windowData["scaleFactor"] as? Number)?.toFloat() ?: 1.0f,
                        rotationAngle = (windowData["rotationAngle"] as? Number)?.toFloat() ?: 0f,
                        zOrder = (windowData["zOrder"] as? Number)?.toInt() ?: 0,
                        isCropping = windowData["isCropping"] as? Boolean ?: false,
                        cropRectRatio = cropRectRatio,
                        isDragEnabled = windowData["isDragEnabled"] as? Boolean ?: false,
                        isScaleEnabled = windowData["isScaleEnabled"] as? Boolean ?: false,
                        isRotationEnabled = windowData["isRotationEnabled"] as? Boolean ?: false,
                        isVisible = windowData["isVisible"] as? Boolean ?: true,
                        isMirrored = windowData["isMirrored"] as? Boolean ?: false,
                        cornerRadius = (windowData["cornerRadius"] as? Number)?.toFloat() ?: 0f,
                        alpha = (windowData["alpha"] as? Number)?.toFloat() ?: 1.0f,
                        isControlEnabled = windowData["isControlEnabled"] as? Boolean ?: false,

                        // 🎯 添加边框参数解析
                        isBorderEnabled = windowData["isBorderEnabled"] as? Boolean ?: false,
                        borderColor = (windowData["borderColor"] as? Number)?.toInt() ?: android.graphics.Color.parseColor("#6B6B6B"),
                        borderWidth = (windowData["borderWidth"] as? Number)?.toFloat() ?: 2f,
                        // 🪟 使用实际显示尺寸而不是基础窗口尺寸
                        baseWindowWidth = actualDisplayWidth,
                        baseWindowHeight = actualDisplayHeight
                    )
                } else {
                    null
                }
            } catch (e: Exception) {
                AppLog.e("【远程窗口管理】解析单个窗口信息失败", e)
                null
            }
        }
    }

    /**
     * 更新窗口列表显示
     */
    private fun updateWindowList(windowList: List<CastWindowInfo>) {
        try {
            // � 统一编辑状态管理：对于文字窗口，使用WindowSettingsManager的编辑状态
            val mergedWindowList = windowList.map { windowInfo ->
                if (windowInfo.connectionId.startsWith("text_")) {
                    // 文字窗口：使用WindowSettingsManager的统一编辑状态
                    val windowSettingsManager = com.example.castapp.manager.WindowSettingsManager.getInstance()
                    val editState = windowSettingsManager.getEditState(windowInfo.connectionId)
                    windowInfo.copy(isEditEnabled = editState)
                } else {
                    // 非文字窗口：保持原状态
                    windowInfo
                }
            }

            this.windowInfoList = mergedWindowList

            AppLog.d("【远程窗口管理】更新窗口列表，数量: ${mergedWindowList.size}")
            mergedWindowList.forEachIndexed { index, windowInfo ->
                if (windowInfo.connectionId.startsWith("text_")) {
                    AppLog.d("【远程窗口管理】文字窗口 $index: ${windowInfo.getDisplayTextWithDevice()}, 遥控端编辑状态: ${windowInfo.isEditEnabled}")
                } else {
                    AppLog.d("【远程窗口管理】窗口 $index: ${windowInfo.getDisplayTextWithDevice()}")
                }
            }

            // 🪟 先通知窗口信息更新回调（用于同步可视化数据），传递更新模式
            onWindowInfoUpdated?.invoke(mergedWindowList, currentUpdateMode)
            AppLog.d("【远程窗口管理】已通知窗口信息更新回调，更新模式: $currentUpdateMode")

            activity?.runOnUiThread {
                // 更新窗口数量显示
                tvWindowCount.text = "${mergedWindowList.size}个窗口"

                // 提交新列表到适配器
                adapter.submitList(mergedWindowList.toList())

                // 显示/隐藏空状态
                if (mergedWindowList.isEmpty()) {
                    rvCastWindows.visibility = View.GONE
                    layoutEmptyState.visibility = View.VISIBLE
                    AppLog.d("【远程窗口管理】显示空状态")
                } else {
                    rvCastWindows.visibility = View.VISIBLE
                    layoutEmptyState.visibility = View.GONE
                    AppLog.d("【远程窗口管理】显示窗口列表")
                }
            } ?: run {
                // 如果没有activity上下文（比如临时处理器），只记录日志
                AppLog.d("【远程窗口管理】无activity上下文，跳过UI更新（临时处理器模式）")
            }

        } catch (e: Exception) {
            AppLog.e("【远程窗口管理】更新窗口列表失败", e)
        }
    }

    /**
     * 显示加载状态
     */
    private fun showLoadingState() {
        activity?.runOnUiThread {
            tvWindowCount.text = "加载中..."
            rvCastWindows.visibility = View.GONE
            layoutEmptyState.visibility = View.VISIBLE

            AppLog.d("【远程窗口管理】显示加载状态")
        }
    }

    /**
     * 显示错误状态
     */
    private fun showErrorState(errorMessage: String) {
        activity?.runOnUiThread {
            tvWindowCount.text = "获取失败"
            rvCastWindows.visibility = View.GONE
            layoutEmptyState.visibility = View.VISIBLE

            AppLog.d("【远程窗口管理】显示错误状态: $errorMessage")
        }
    }

    /**
     * 🔄 设置窗口操作监听器（同步开关开启时）
     */
    private fun setupWindowOperationListeners() {
        // 设置变换功能开关监听器
        adapter.setOnTransformSwitchListener(object : WindowManagerAdapter.OnTransformSwitchListener {
            override fun onDragSwitchChanged(connectionId: String, isEnabled: Boolean) {
                sendWindowTransformControl(connectionId, "drag_enabled", mapOf("enabled" to isEnabled))
            }

            override fun onScaleSwitchChanged(connectionId: String, isEnabled: Boolean) {
                sendWindowTransformControl(connectionId, "scale_enabled", mapOf("enabled" to isEnabled))
            }

            override fun onRotationSwitchChanged(connectionId: String, isEnabled: Boolean) {
                sendWindowTransformControl(connectionId, "rotation_enabled", mapOf("enabled" to isEnabled))
            }
        })

        // 设置裁剪开关监听器
        adapter.setOnCropSwitchListener(object : WindowManagerAdapter.OnCropSwitchListener {
            override fun onCropSwitchChanged(connectionId: String, isEnabled: Boolean) {
                sendWindowTransformControl(connectionId, "crop", mapOf("enabled" to isEnabled))
            }
        })

        // 设置可见性开关监听器
        adapter.setOnVisibilitySwitchListener(object : WindowManagerAdapter.OnVisibilitySwitchListener {
            override fun onVisibilitySwitchChanged(connectionId: String, isVisible: Boolean) {
                sendWindowTransformControl(connectionId, "visibility", mapOf("visible" to isVisible))
            }
        })

        // 设置镜像开关监听器
        adapter.setOnMirrorSwitchListener(object : WindowManagerAdapter.OnMirrorSwitchListener {
            override fun onMirrorSwitchChanged(connectionId: String, isEnabled: Boolean) {
                sendWindowTransformControl(connectionId, "mirror", mapOf("enabled" to isEnabled))
            }
        })

        // 设置圆角半径变化监听器
        adapter.setOnCornerRadiusChangeListener(object : WindowManagerAdapter.OnCornerRadiusChangeListener {
            override fun onCornerRadiusChanged(connectionId: String, cornerRadius: Float) {
                val transformData = mapOf("radius" to cornerRadius)
                // 🎯 关键修复：同时更新接收端和遥控端本地可视化窗口
                sendWindowTransformControl(connectionId, "corner_radius", transformData)
                updateLocalVisualizationBorderParams(connectionId, "corner_radius", transformData)
                AppLog.d("【遥控端圆角半径】已同步更新接收端和本地可视化窗口: $connectionId -> ${cornerRadius}dp")
            }
        })

        // 设置透明度变化监听器
        adapter.setOnAlphaChangeListener(object : WindowManagerAdapter.OnAlphaChangeListener {
            override fun onAlphaChanged(connectionId: String, alpha: Float) {
                val transformData = mapOf("alpha" to alpha)
                // 🎯 关键修复：同时更新接收端和遥控端本地可视化窗口
                sendWindowTransformControl(connectionId, "alpha", transformData)
                updateLocalVisualizationBorderParams(connectionId, "alpha", transformData)
                AppLog.d("【遥控端透明度】已同步更新接收端和本地可视化窗口: $connectionId -> ${(alpha * 100).toInt()}%")
            }
        })

        // 设置边框开关监听器
        adapter.setOnBorderSwitchListener(object : WindowManagerAdapter.OnBorderSwitchListener {
            override fun onBorderSwitchChanged(connectionId: String, isEnabled: Boolean) {
                val transformData = mapOf("enabled" to isEnabled)
                // 🎯 关键修复：同时更新接收端和遥控端本地可视化窗口
                sendWindowTransformControl(connectionId, "border", transformData)
                updateLocalVisualizationBorderParams(connectionId, "border", transformData)
                AppLog.d("【遥控端边框开关】已同步更新接收端和本地可视化窗口: $connectionId -> 边框${if (isEnabled) "开启" else "关闭"}")
            }
        })

        // 设置边框颜色变化监听器
        adapter.setOnBorderColorChangeListener(object : WindowManagerAdapter.OnBorderColorChangeListener {
            override fun onBorderColorChanged(connectionId: String, color: Int) {
                val transformData = mapOf("color" to color)
                // 🎯 关键修复：同时更新接收端和遥控端本地可视化窗口
                sendWindowTransformControl(connectionId, "border_color", transformData)
                updateLocalVisualizationBorderParams(connectionId, "border_color", transformData)
                AppLog.d("【遥控端边框颜色】已同步更新接收端和本地可视化窗口: $connectionId -> ${String.format("#%06X", 0xFFFFFF and color)}")
            }
        })

        // 设置边框宽度变化监听器
        adapter.setOnBorderWidthChangeListener(object : WindowManagerAdapter.OnBorderWidthChangeListener {
            override fun onBorderWidthChanged(connectionId: String, width: Float) {
                val transformData = mapOf("width" to width)
                // 🎯 关键修复：同时更新接收端和遥控端本地可视化窗口
                sendWindowTransformControl(connectionId, "border_width", transformData)
                updateLocalVisualizationBorderParams(connectionId, "border_width", transformData)
                AppLog.d("【遥控端边框宽度】已同步更新接收端和本地可视化窗口: $connectionId -> ${width}dp")
            }
        })

        // 📝 设置编辑开关监听器（仅文字窗口）
        adapter.setOnEditSwitchListener(object : WindowManagerAdapter.OnEditSwitchListener {
            override fun onEditSwitchChanged(connectionId: String, isEnabled: Boolean) {
                handleTextWindowEditModeToggle(connectionId, isEnabled)
            }
        })

        // 🏷️ 设置备注变更监听器
        adapter.setOnNoteChangeListener(object : WindowManagerAdapter.OnNoteChangeListener {
            override fun onNoteChanged(connectionId: String, note: String) {
                // 🎯 关键修复：遥控端备注变更时，发送同步消息给接收端
                sendNoteChangeToReceiver(connectionId, note)
                AppLog.d("【遥控端备注同步】备注变更已发送给接收端: $connectionId -> $note")
            }
        })

        AppLog.d("【远程窗口同步】窗口操作监听器已设置")
    }

    /**
     * 🔄 清除窗口操作监听器（同步开关关闭时）
     */
    private fun clearWindowOperationListeners() {
        if (::adapter.isInitialized) {
            // 🔧 修复：将所有监听器设置为null，确保操作控件时不发送WebSocket消息
            adapter.setOnTransformSwitchListener(null)
            adapter.setOnCropSwitchListener(null)
            adapter.setOnVisibilitySwitchListener(null)
            adapter.setOnMirrorSwitchListener(null)
            adapter.setOnCornerRadiusChangeListener(null)
            adapter.setOnAlphaChangeListener(null)
            adapter.setOnBorderSwitchListener(null)
            adapter.setOnBorderColorChangeListener(null)
            adapter.setOnBorderWidthChangeListener(null)
            // 🎯 关键修复：编辑开关监听器不清除，保持编辑功能始终可用
            // adapter.setOnEditSwitchListener(null) // 注释掉这行，保持编辑功能独立
        }

        AppLog.d("【远程窗口同步】窗口操作监听器已清除（编辑开关监听器保留）")
    }

    /**
     * 🎯 设置编辑开关监听器（独立于同步开关）
     */
    private fun setupEditSwitchListener() {
        if (::adapter.isInitialized) {
            // 📝 设置编辑开关监听器（仅文字窗口）
            adapter.setOnEditSwitchListener(object : WindowManagerAdapter.OnEditSwitchListener {
                override fun onEditSwitchChanged(connectionId: String, isEnabled: Boolean) {
                    handleTextWindowEditModeToggle(connectionId, isEnabled)
                }
            })
            AppLog.d("【远程窗口管理】编辑开关监听器已设置（独立于同步开关）")
        }
    }

    /**
     * 🔄 发送窗口变换控制消息
     */
    private fun sendWindowTransformControl(targetWindowId: String, transformType: String, transformData: Map<String, Any>) {
        try {
            val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            val message = ControlMessage.createRemoteWindowTransformControl(
                connectionId = remoteReceiverConnection.id,
                targetWindowId = targetWindowId,
                transformType = transformType,
                transformData = transformData
            )

            manager.sendWindowTransformControl(remoteReceiverConnection, message)

            AppLog.d("【远程窗口同步】发送窗口变换控制: $targetWindowId -> $transformType")

        } catch (e: Exception) {
            AppLog.e("【远程窗口同步】发送窗口变换控制失败", e)
            Toast.makeText(requireContext(), "窗口控制失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 🎯 新增：更新遥控端本地可视化窗口的边框参数
     */
    private fun updateLocalVisualizationBorderParams(targetWindowId: String, transformType: String, transformData: Map<String, Any>) {
        try {
            // 获取控制对话框的窗口可视化容器
            val controlDialog = com.example.castapp.manager.RemoteReceiverManager.getInstance()
                .getActiveControlDialog(remoteReceiverConnection.id)

            if (controlDialog == null) {
                AppLog.w("【遥控端边框更新】未找到活跃的控制对话框")
                return
            }

            val windowVisualizationView = controlDialog.getWindowVisualizationView()
            if (windowVisualizationView == null) {
                AppLog.w("【遥控端边框更新】未找到窗口可视化容器")
                return
            }

            // 获取当前可视化数据列表
            val currentVisualizationDataList = windowVisualizationView.getVisualizationDataList().toMutableList()

            // 查找目标窗口的可视化数据
            val targetDataIndex = currentVisualizationDataList.indexOfFirst { it.connectionId == targetWindowId }
            if (targetDataIndex == -1) {
                AppLog.w("【遥控端边框更新】未找到目标窗口的可视化数据: $targetWindowId")
                return
            }

            val targetData = currentVisualizationDataList[targetDataIndex]

            // 根据变换类型更新对应的参数
            val updatedData = when (transformType) {
                "border" -> {
                    val enabled = transformData["enabled"] as? Boolean ?: return
                    targetData.copy(isBorderEnabled = enabled)
                }
                "border_color" -> {
                    val color = (transformData["color"] as? Number)?.toInt() ?: return
                    targetData.copy(borderColor = color)
                }
                "border_width" -> {
                    val width = (transformData["width"] as? Number)?.toFloat() ?: return
                    targetData.copy(borderWidth = width)
                }
                "corner_radius" -> {
                    val radius = (transformData["radius"] as? Number)?.toFloat() ?: return
                    targetData.copy(cornerRadius = radius)
                }
                "alpha" -> {
                    val alpha = (transformData["alpha"] as? Number)?.toFloat() ?: return
                    targetData.copy(alpha = alpha)
                }
                else -> {
                    AppLog.w("【遥控端边框更新】不支持的变换类型: $transformType")
                    return
                }
            }

            // 更新可视化数据列表
            currentVisualizationDataList[targetDataIndex] = updatedData

            // 在UI线程中更新可视化窗口
            activity?.runOnUiThread {
                windowVisualizationView.updateVisualizationData(currentVisualizationDataList)
                AppLog.d("【遥控端边框更新】本地可视化窗口已更新: $targetWindowId -> $transformType")
            }

        } catch (e: Exception) {
            AppLog.e("【遥控端边框更新】更新本地可视化窗口失败", e)
        }
    }

    /**
     * 🏷️ 发送备注变更消息给接收端
     */
    private fun sendNoteChangeToReceiver(connectionId: String, note: String) {
        try {
            val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            val message = ControlMessage.createRemoteNoteSync(
                connectionId = remoteReceiverConnection.id,
                targetConnectionId = connectionId,
                note = note
            )

            manager.sendNoteChangeMessage(remoteReceiverConnection, message)
            AppLog.d("【遥控端备注同步】备注变更消息已发送: $connectionId -> $note")

        } catch (e: Exception) {
            AppLog.e("【遥控端备注同步】发送备注变更消息失败", e)
            Toast.makeText(requireContext(), "备注同步失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 🏷️ 更新窗口备注信息（从接收端同步过来）
     */
    fun updateWindowNote(connectionId: String, note: String) {
        try {
            AppLog.d("【遥控端备注同步】更新窗口备注: $connectionId -> $note")

            // 在UI线程中更新适配器
            activity?.runOnUiThread {
                adapter.updateWindowNote(connectionId, note)
                AppLog.d("【遥控端备注同步】窗口备注已更新: $connectionId -> $note")
            }

        } catch (e: Exception) {
            AppLog.e("【遥控端备注同步】更新窗口备注失败", e)
        }
    }

    /**
     * 📝 处理文字窗口编辑模式切换
     */
    private fun handleTextWindowEditModeToggle(connectionId: String, isEnabled: Boolean) {
        try {
            AppLog.d("【远程窗口管理】处理文字窗口编辑模式切换: $connectionId -> $isEnabled")

            // � 统一编辑状态管理：使用WindowSettingsManager保存编辑状态
            val windowSettingsManager = com.example.castapp.manager.WindowSettingsManager.getInstance()
            windowSettingsManager.setEditState(connectionId, isEnabled)

            if (isEnabled) {
                // 🎯 修复：开启编辑模式只在遥控端创建编辑界面，不发送消息到接收端
                createRemoteTextWindowForEditing(connectionId)
                AppLog.d("【远程窗口管理】遥控端文字窗口已进入编辑模式: $connectionId")
            } else {
                // 🎯 修复：关闭编辑模式时，隐藏编辑面板并根据同步开关决定是否同步
                hideRemoteTextWindowEditPanelWithSync(connectionId, isSyncEnabled)
            }

        } catch (e: Exception) {
            AppLog.e("【远程窗口管理】处理文字窗口编辑模式切换失败", e)
        }
    }

    /**
     * 📝 为编辑创建遥控端文字窗口
     */
    private fun createRemoteTextWindowForEditing(connectionId: String) {
        try {
            // 获取对应的文字窗口信息
            val windowInfo = windowInfoList.find { it.connectionId == connectionId }
            if (windowInfo == null) {
                AppLog.w("【远程窗口管理】未找到文字窗口信息: $connectionId")
                return
            }

            // 通过RemoteReceiverManager获取对应的RemoteReceiverControlDialog
            val receiverManager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            val controlDialog = receiverManager.getActiveControlDialog(remoteReceiverConnection.id)

            if (controlDialog == null) {
                AppLog.w("【远程窗口管理】未找到对应的远程控制对话框: ${remoteReceiverConnection.id}")
                return
            }

            // 获取控制对话框的窗口可视化容器
            val windowVisualizationView = controlDialog.getWindowVisualizationView()
            if (windowVisualizationView == null) {
                AppLog.w("【远程窗口管理】未找到窗口可视化容器")
                return
            }

            // 创建遥控端文字窗口管理器
            val remoteTextWindowManager = com.example.castapp.ui.windowsettings.RemoteTextWindowManager(
                context = requireContext(),
                textId = connectionId,
                initialTextContent = windowInfo.deviceName ?: "默认文字",
                remoteReceiverConnection = remoteReceiverConnection,
                windowVisualizationView = windowVisualizationView
            )

            // 🎯 设置同步回调函数，直接返回当前对话框的同步开关状态
            remoteTextWindowManager.setSyncCallback { isSyncEnabled }

            // 🎯 保存到全局管理器集合中
            receiverManager.addGlobalRemoteTextWindowManager(remoteReceiverConnection.id, connectionId, remoteTextWindowManager)

            // 显示编辑面板
            remoteTextWindowManager.showEditPanel()

            AppLog.d("【远程窗口管理】遥控端文字窗口编辑模式已启动: $connectionId")

        } catch (e: Exception) {
            AppLog.e("【远程窗口管理】创建遥控端文字窗口失败", e)
        }
    }

    /**
     * 📝 隐藏遥控端文字窗口编辑面板（带同步控制）
     */
    private fun hideRemoteTextWindowEditPanelWithSync(connectionId: String, shouldSync: Boolean) {
        try {
            AppLog.d("【远程窗口管理】隐藏遥控端文字窗口编辑面板: $connectionId, 是否同步: $shouldSync")

            // 🎯 修复：使用全局管理器来查找和隐藏编辑面板
            val receiverManager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            val remoteTextWindowManager = receiverManager.getGlobalRemoteTextWindowManager(remoteReceiverConnection.id, connectionId)

            if (remoteTextWindowManager != null) {
                // 隐藏编辑面板并根据参数决定是否同步
                remoteTextWindowManager.hideEditPanelWithSync(shouldSync)

                // 从全局管理器集合中移除
                receiverManager.removeGlobalRemoteTextWindowManager(remoteReceiverConnection.id, connectionId)

                if (shouldSync) {
                    AppLog.d("【远程窗口管理】遥控端文字窗口编辑面板已隐藏并同步到接收端: $connectionId")
                } else {
                    AppLog.d("【远程窗口管理】遥控端文字窗口编辑面板已隐藏，内容仅保存在遥控端: $connectionId")
                }
            } else {
                AppLog.w("【远程窗口管理】未找到对应的遥控端文字窗口管理器: $connectionId")
            }

        } catch (e: Exception) {
            AppLog.e("【远程窗口管理】隐藏编辑面板失败", e)
        }
    }

    /**
     * 📝 获取同步开关状态
     */
    fun isSyncEnabled(): Boolean {
        return isSyncEnabled
    }

    /**
     * 🪟 注册对话框到管理器
     */
    private fun registerDialog() {
        val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
        manager.registerWindowManagerDialog(remoteReceiverConnection.id, this)
        AppLog.d("【远程窗口管理】对话框已注册: ${remoteReceiverConnection.id}")
    }

    /**
     * 🪟 注销对话框
     */
    private fun unregisterDialog() {
        val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
        manager.unregisterWindowManagerDialog(remoteReceiverConnection.id)
        AppLog.d("【远程窗口管理】对话框已注销: ${remoteReceiverConnection.id}")
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)

        // 注销对话框
        unregisterDialog()

        onDialogDismissed?.invoke()
        AppLog.d("【远程窗口管理】对话框已关闭")
    }
}
